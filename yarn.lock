# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@ampproject/remapping@^2.2.0":
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/@ampproject/remapping/download/@ampproject/remapping-2.3.0.tgz#ed441b6fa600072520ce18b43d2c8cc8caecc7f4"
  integrity sha1-7UQbb6YAByUgzhi0PSyMyMrsx/Q=
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.24"

"@antfu/utils@^0.7.10":
  version "0.7.10"
  resolved "http://r.npm.sankuai.com/@antfu/utils/download/@antfu/utils-0.7.10.tgz#ae829f170158e297a9b6a28f161a8e487d00814d"
  integrity sha1-roKfFwFY4peptqKPFhqOSH0AgU0=

"@asamuzakjp/css-color@^3.1.2":
  version "3.2.0"
  resolved "http://r.npm.sankuai.com/@asamuzakjp/css-color/download/@asamuzakjp/css-color-3.2.0.tgz#cc42f5b85c593f79f1fa4f25d2b9b321e61d1794"
  integrity sha1-zEL1uFxZP3nx+k8l0rmzIeYdF5Q=
  dependencies:
    "@csstools/css-calc" "^2.1.3"
    "@csstools/css-color-parser" "^3.0.9"
    "@csstools/css-parser-algorithms" "^3.0.4"
    "@csstools/css-tokenizer" "^3.0.3"
    lru-cache "^10.4.3"

"@babel/code-frame@^7.26.2", "@babel/code-frame@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/code-frame/download/@babel/code-frame-7.27.1.tgz#200f715e66d52a23b221a9435534a91cc13ad5be"
  integrity sha1-IA9xXmbVKiOyIalDVTSpHME61b4=
  dependencies:
    "@babel/helper-validator-identifier" "^7.27.1"
    js-tokens "^4.0.0"
    picocolors "^1.1.1"

"@babel/compat-data@^7.27.2":
  version "7.27.3"
  resolved "http://r.npm.sankuai.com/@babel/compat-data/download/@babel/compat-data-7.27.3.tgz#cc49c2ac222d69b889bf34c795f537c0c6311111"
  integrity sha1-zEnCrCItabiJvzTHlfU3wMYxERE=

"@babel/core@^7.23.0", "@babel/core@^7.27.1":
  version "7.27.3"
  resolved "http://r.npm.sankuai.com/@babel/core/download/@babel/core-7.27.3.tgz#d7d05502bccede3cab36373ed142e6a1df554c2f"
  integrity sha1-19BVArzO3jyrNjc+0ULmod9VTC8=
  dependencies:
    "@ampproject/remapping" "^2.2.0"
    "@babel/code-frame" "^7.27.1"
    "@babel/generator" "^7.27.3"
    "@babel/helper-compilation-targets" "^7.27.2"
    "@babel/helper-module-transforms" "^7.27.3"
    "@babel/helpers" "^7.27.3"
    "@babel/parser" "^7.27.3"
    "@babel/template" "^7.27.2"
    "@babel/traverse" "^7.27.3"
    "@babel/types" "^7.27.3"
    convert-source-map "^2.0.0"
    debug "^4.1.0"
    gensync "^1.0.0-beta.2"
    json5 "^2.2.3"
    semver "^6.3.1"

"@babel/generator@^7.27.3":
  version "7.27.3"
  resolved "http://r.npm.sankuai.com/@babel/generator/download/@babel/generator-7.27.3.tgz#ef1c0f7cfe3b5fc8cbb9f6cc69f93441a68edefc"
  integrity sha1-7xwPfP47X8jLufbMafk0QaaO3vw=
  dependencies:
    "@babel/parser" "^7.27.3"
    "@babel/types" "^7.27.3"
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.25"
    jsesc "^3.0.2"

"@babel/helper-annotate-as-pure@^7.27.1":
  version "7.27.3"
  resolved "http://r.npm.sankuai.com/@babel/helper-annotate-as-pure/download/@babel/helper-annotate-as-pure-7.27.3.tgz#f31fd86b915fc4daf1f3ac6976c59be7084ed9c5"
  integrity sha1-8x/Ya5FfxNrx86xpdsWb5whO2cU=
  dependencies:
    "@babel/types" "^7.27.3"

"@babel/helper-compilation-targets@^7.27.2":
  version "7.27.2"
  resolved "http://r.npm.sankuai.com/@babel/helper-compilation-targets/download/@babel/helper-compilation-targets-7.27.2.tgz#46a0f6efab808d51d29ce96858dd10ce8732733d"
  integrity sha1-RqD276uAjVHSnOloWN0Qzocycz0=
  dependencies:
    "@babel/compat-data" "^7.27.2"
    "@babel/helper-validator-option" "^7.27.1"
    browserslist "^4.24.0"
    lru-cache "^5.1.1"
    semver "^6.3.1"

"@babel/helper-create-class-features-plugin@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/helper-create-class-features-plugin/download/@babel/helper-create-class-features-plugin-7.27.1.tgz#5bee4262a6ea5ddc852d0806199eb17ca3de9281"
  integrity sha1-W+5CYqbqXdyFLQgGGZ6xfKPekoE=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.27.1"
    "@babel/helper-member-expression-to-functions" "^7.27.1"
    "@babel/helper-optimise-call-expression" "^7.27.1"
    "@babel/helper-replace-supers" "^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.27.1"
    "@babel/traverse" "^7.27.1"
    semver "^6.3.1"

"@babel/helper-member-expression-to-functions@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/helper-member-expression-to-functions/download/@babel/helper-member-expression-to-functions-7.27.1.tgz#ea1211276be93e798ce19037da6f06fbb994fa44"
  integrity sha1-6hIRJ2vpPnmM4ZA32m8G+7mU+kQ=
  dependencies:
    "@babel/traverse" "^7.27.1"
    "@babel/types" "^7.27.1"

"@babel/helper-module-imports@^7.25.9", "@babel/helper-module-imports@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/helper-module-imports/download/@babel/helper-module-imports-7.27.1.tgz#7ef769a323e2655e126673bb6d2d6913bbead204"
  integrity sha1-fvdpoyPiZV4SZnO7bS1pE7vq0gQ=
  dependencies:
    "@babel/traverse" "^7.27.1"
    "@babel/types" "^7.27.1"

"@babel/helper-module-transforms@^7.27.3":
  version "7.27.3"
  resolved "http://r.npm.sankuai.com/@babel/helper-module-transforms/download/@babel/helper-module-transforms-7.27.3.tgz#db0bbcfba5802f9ef7870705a7ef8788508ede02"
  integrity sha1-2wu8+6WAL573hwcFp++HiFCO3gI=
  dependencies:
    "@babel/helper-module-imports" "^7.27.1"
    "@babel/helper-validator-identifier" "^7.27.1"
    "@babel/traverse" "^7.27.3"

"@babel/helper-optimise-call-expression@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/helper-optimise-call-expression/download/@babel/helper-optimise-call-expression-7.27.1.tgz#c65221b61a643f3e62705e5dd2b5f115e35f9200"
  integrity sha1-xlIhthpkPz5icF5d0rXxFeNfkgA=
  dependencies:
    "@babel/types" "^7.27.1"

"@babel/helper-plugin-utils@^7.10.4", "@babel/helper-plugin-utils@^7.26.5", "@babel/helper-plugin-utils@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/helper-plugin-utils/download/@babel/helper-plugin-utils-7.27.1.tgz#ddb2f876534ff8013e6c2b299bf4d39b3c51d44c"
  integrity sha1-3bL4dlNP+AE+bCspm/TTmzxR1Ew=

"@babel/helper-replace-supers@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/helper-replace-supers/download/@babel/helper-replace-supers-7.27.1.tgz#b1ed2d634ce3bdb730e4b52de30f8cccfd692bc0"
  integrity sha1-se0tY0zjvbcw5LUt4w+MzP1pK8A=
  dependencies:
    "@babel/helper-member-expression-to-functions" "^7.27.1"
    "@babel/helper-optimise-call-expression" "^7.27.1"
    "@babel/traverse" "^7.27.1"

"@babel/helper-skip-transparent-expression-wrappers@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/helper-skip-transparent-expression-wrappers/download/@babel/helper-skip-transparent-expression-wrappers-7.27.1.tgz#62bb91b3abba8c7f1fec0252d9dbea11b3ee7a56"
  integrity sha1-YruRs6u6jH8f7AJS2dvqEbPuelY=
  dependencies:
    "@babel/traverse" "^7.27.1"
    "@babel/types" "^7.27.1"

"@babel/helper-string-parser@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/helper-string-parser/download/@babel/helper-string-parser-7.27.1.tgz#54da796097ab19ce67ed9f88b47bb2ec49367687"
  integrity sha1-VNp5YJerGc5n7Z+ItHuy7Ek2doc=

"@babel/helper-validator-identifier@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/helper-validator-identifier/download/@babel/helper-validator-identifier-7.27.1.tgz#a7054dcc145a967dd4dc8fee845a57c1316c9df8"
  integrity sha1-pwVNzBRaln3U3I/uhFpXwTFsnfg=

"@babel/helper-validator-option@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/helper-validator-option/download/@babel/helper-validator-option-7.27.1.tgz#fa52f5b1e7db1ab049445b421c4471303897702f"
  integrity sha1-+lL1sefbGrBJRFtCHERxMDiXcC8=

"@babel/helpers@^7.27.3":
  version "7.27.3"
  resolved "http://r.npm.sankuai.com/@babel/helpers/download/@babel/helpers-7.27.3.tgz#387d65d279290e22fe7a47a8ffcd2d0c0184edd0"
  integrity sha1-OH1l0nkpDiL+ekeo/80tDAGE7dA=
  dependencies:
    "@babel/template" "^7.27.2"
    "@babel/types" "^7.27.3"

"@babel/parser@^7.26.9", "@babel/parser@^7.27.2", "@babel/parser@^7.27.3":
  version "7.27.3"
  resolved "http://r.npm.sankuai.com/@babel/parser/download/@babel/parser-7.27.3.tgz#1b7533f0d908ad2ac545c4d05cbe2fb6dc8cfaaf"
  integrity sha1-G3Uz8NkIrSrFRcTQXL4vttyM+q8=
  dependencies:
    "@babel/types" "^7.27.3"

"@babel/plugin-proposal-decorators@^7.23.0":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-proposal-decorators/download/@babel/plugin-proposal-decorators-7.27.1.tgz#3686f424b2f8b2fee7579aa4df133a4f5244a596"
  integrity sha1-Nob0JLL4sv7nV5qk3xM6T1JEpZY=
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/plugin-syntax-decorators" "^7.27.1"

"@babel/plugin-syntax-decorators@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-syntax-decorators/download/@babel/plugin-syntax-decorators-7.27.1.tgz#ee7dd9590aeebc05f9d4c8c0560007b05979a63d"
  integrity sha1-7n3ZWQruvAX51MjAVgAHsFl5pj0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-syntax-import-attributes@^7.22.5":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-syntax-import-attributes/download/@babel/plugin-syntax-import-attributes-7.27.1.tgz#34c017d54496f9b11b61474e7ea3dfd5563ffe07"
  integrity sha1-NMAX1USW+bEbYUdOfqPf1VY//gc=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-syntax-import-meta@^7.10.4":
  version "7.10.4"
  resolved "http://r.npm.sankuai.com/@babel/plugin-syntax-import-meta/download/@babel/plugin-syntax-import-meta-7.10.4.tgz#ee601348c370fa334d2207be158777496521fd51"
  integrity sha1-7mATSMNw+jNNIge+FYd3SWUh/VE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-jsx@^7.25.9":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-syntax-jsx/download/@babel/plugin-syntax-jsx-7.27.1.tgz#2f9beb5eff30fa507c5532d107daac7b888fa34c"
  integrity sha1-L5vrXv8w+lB8VTLRB9qse4iPo0w=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-syntax-typescript@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-syntax-typescript/download/@babel/plugin-syntax-typescript-7.27.1.tgz#5147d29066a793450f220c63fa3a9431b7e6dd18"
  integrity sha1-UUfSkGank0UPIgxj+jqUMbfm3Rg=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-typescript@^7.22.15", "@babel/plugin-transform-typescript@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-typescript/download/@babel/plugin-transform-typescript-7.27.1.tgz#d3bb65598bece03f773111e88cc4e8e5070f1140"
  integrity sha1-07tlWYvs4D93MRHojMTo5QcPEUA=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.27.1"
    "@babel/helper-create-class-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.27.1"
    "@babel/plugin-syntax-typescript" "^7.27.1"

"@babel/template@^7.26.9", "@babel/template@^7.27.2":
  version "7.27.2"
  resolved "http://r.npm.sankuai.com/@babel/template/download/@babel/template-7.27.2.tgz#fa78ceed3c4e7b63ebf6cb39e5852fca45f6809d"
  integrity sha1-+njO7TxOe2Pr9ss55YUvykX2gJ0=
  dependencies:
    "@babel/code-frame" "^7.27.1"
    "@babel/parser" "^7.27.2"
    "@babel/types" "^7.27.1"

"@babel/traverse@^7.26.9", "@babel/traverse@^7.27.1", "@babel/traverse@^7.27.3":
  version "7.27.3"
  resolved "http://r.npm.sankuai.com/@babel/traverse/download/@babel/traverse-7.27.3.tgz#8b62a6c2d10f9d921ba7339c90074708509cffae"
  integrity sha1-i2KmwtEPnZIbpzOckAdHCFCc/64=
  dependencies:
    "@babel/code-frame" "^7.27.1"
    "@babel/generator" "^7.27.3"
    "@babel/parser" "^7.27.3"
    "@babel/template" "^7.27.2"
    "@babel/types" "^7.27.3"
    debug "^4.3.1"
    globals "^11.1.0"

"@babel/types@^7.26.9", "@babel/types@^7.27.1", "@babel/types@^7.27.3":
  version "7.27.3"
  resolved "http://r.npm.sankuai.com/@babel/types/download/@babel/types-7.27.3.tgz#c0257bedf33aad6aad1f406d35c44758321eb3ec"
  integrity sha1-wCV77fM6rWqtH0BtNcRHWDIes+w=
  dependencies:
    "@babel/helper-string-parser" "^7.27.1"
    "@babel/helper-validator-identifier" "^7.27.1"

"@csstools/color-helpers@^5.0.2":
  version "5.0.2"
  resolved "http://r.npm.sankuai.com/@csstools/color-helpers/download/@csstools/color-helpers-5.0.2.tgz#82592c9a7c2b83c293d9161894e2a6471feb97b8"
  integrity sha1-glksmnwrg8KT2RYYlOKmRx/rl7g=

"@csstools/css-calc@^2.1.3":
  version "2.1.4"
  resolved "http://r.npm.sankuai.com/@csstools/css-calc/download/@csstools/css-calc-2.1.4.tgz#8473f63e2fcd6e459838dd412401d5948f224c65"
  integrity sha1-hHP2Pi/NbkWYON1BJAHVlI8iTGU=

"@csstools/css-color-parser@^3.0.9":
  version "3.0.9"
  resolved "http://r.npm.sankuai.com/@csstools/css-color-parser/download/@csstools/css-color-parser-3.0.9.tgz#8d81b77d6f211495b5100ec4cad4c8828de49f6b"
  integrity sha1-jYG3fW8hFJW1EA7EytTIgo3kn2s=
  dependencies:
    "@csstools/color-helpers" "^5.0.2"
    "@csstools/css-calc" "^2.1.3"

"@csstools/css-parser-algorithms@^3.0.4":
  version "3.0.5"
  resolved "http://r.npm.sankuai.com/@csstools/css-parser-algorithms/download/@csstools/css-parser-algorithms-3.0.5.tgz#5755370a9a29abaec5515b43c8b3f2cf9c2e3076"
  integrity sha1-V1U3Cpopq67FUVtDyLPyz5wuMHY=

"@csstools/css-tokenizer@^3.0.3":
  version "3.0.4"
  resolved "http://r.npm.sankuai.com/@csstools/css-tokenizer/download/@csstools/css-tokenizer-3.0.4.tgz#333fedabc3fd1a8e5d0100013731cf19e6a8c5d3"
  integrity sha1-Mz/tq8P9Go5dAQABNzHPGeaoxdM=

"@esbuild/aix-ppc64@0.25.5":
  version "0.25.5"
  resolved "http://r.npm.sankuai.com/@esbuild/aix-ppc64/download/@esbuild/aix-ppc64-0.25.5.tgz#4e0f91776c2b340e75558f60552195f6fad09f18"
  integrity sha1-Tg+Rd2wrNA51VY9gVSGV9vrQnxg=

"@esbuild/android-arm64@0.25.5":
  version "0.25.4"
  resolved "http://r.npm.sankuai.com/@esbuild/android-arm64/download/@esbuild/android-arm64-0.25.4.tgz#d11d4fc299224e729e2190cacadbcc00e7a9fd67"
  integrity sha1-0R1PwpkiTnKeIZDKytvMAOep/Wc=

"@esbuild/android-arm@0.25.5":
  version "0.25.4"
  resolved "http://r.npm.sankuai.com/@esbuild/android-arm/download/@esbuild/android-arm-0.25.4.tgz#5660bd25080553dd2a28438f2a401a29959bd9b1"
  integrity sha1-VmC9JQgFU90qKEOPKkAaKZWb2bE=

"@esbuild/android-x64@0.25.5":
  version "0.25.4"
  resolved "http://r.npm.sankuai.com/@esbuild/android-x64/download/@esbuild/android-x64-0.25.4.tgz#18ddde705bf984e8cd9efec54e199ac18bc7bee1"
  integrity sha1-GN3ecFv5hOjNnv7FThmawYvHvuE=

"@esbuild/darwin-arm64@0.25.5":
  version "0.25.5"
  resolved "http://r.npm.sankuai.com/@esbuild/darwin-arm64/download/@esbuild/darwin-arm64-0.25.5.tgz#49d8bf8b1df95f759ac81eb1d0736018006d7e34"
  integrity sha1-Sdi/ix35X3WayB6x0HNgGABtfjQ=

"@esbuild/darwin-x64@0.25.5":
  version "0.25.4"
  resolved "http://r.npm.sankuai.com/@esbuild/darwin-x64/download/@esbuild/darwin-x64-0.25.4.tgz#e6813fdeba0bba356cb350a4b80543fbe66bf26f"
  integrity sha1-5oE/3roLujVss1CkuAVD++Zr8m8=

"@esbuild/freebsd-arm64@0.25.5":
  version "0.25.4"
  resolved "http://r.npm.sankuai.com/@esbuild/freebsd-arm64/download/@esbuild/freebsd-arm64-0.25.4.tgz#dc11a73d3ccdc308567b908b43c6698e850759be"
  integrity sha1-3BGnPTzNwwhWe5CLQ8ZpjoUHWb4=

"@esbuild/freebsd-x64@0.25.5":
  version "0.25.4"
  resolved "http://r.npm.sankuai.com/@esbuild/freebsd-x64/download/@esbuild/freebsd-x64-0.25.4.tgz#91da08db8bd1bff5f31924c57a81dab26e93a143"
  integrity sha1-kdoI24vRv/XzGSTFeoHasm6ToUM=

"@esbuild/linux-arm64@0.25.5":
  version "0.25.5"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-arm64/download/@esbuild/linux-arm64-0.25.5.tgz#f7b7c8f97eff8ffd2e47f6c67eb5c9765f2181b8"
  integrity sha1-97fI+X7/j/0uR/bGfrXJdl8hgbg=

"@esbuild/linux-arm@0.25.5":
  version "0.25.5"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-arm/download/@esbuild/linux-arm-0.25.5.tgz#2a0be71b6cd8201fa559aea45598dffabc05d911"
  integrity sha1-KgvnG2zYIB+lWa6kVZjf+rwF2RE=

"@esbuild/linux-ia32@0.25.5":
  version "0.25.5"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-ia32/download/@esbuild/linux-ia32-0.25.5.tgz#763414463cd9ea6fa1f96555d2762f9f84c61783"
  integrity sha1-djQURjzZ6m+h+WVV0nYvn4TGF4M=

"@esbuild/linux-loong64@0.25.5":
  version "0.25.5"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-loong64/download/@esbuild/linux-loong64-0.25.5.tgz#428cf2213ff786a502a52c96cf29d1fcf1eb8506"
  integrity sha1-QozyIT/3hqUCpSyWzynR/PHrhQY=

"@esbuild/linux-mips64el@0.25.5":
  version "0.25.5"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-mips64el/download/@esbuild/linux-mips64el-0.25.5.tgz#5cbcc7fd841b4cd53358afd33527cd394e325d96"
  integrity sha1-XLzH/YQbTNUzWK/TNSfNOU4yXZY=

"@esbuild/linux-ppc64@0.25.5":
  version "0.25.5"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-ppc64/download/@esbuild/linux-ppc64-0.25.5.tgz#0d954ab39ce4f5e50f00c4f8c4fd38f976c13ad9"
  integrity sha1-DZVKs5zk9eUPAMT4xP04+XbBOtk=

"@esbuild/linux-riscv64@0.25.5":
  version "0.25.5"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-riscv64/download/@esbuild/linux-riscv64-0.25.5.tgz#0e7dd30730505abd8088321e8497e94b547bfb1e"
  integrity sha1-Dn3TBzBQWr2AiDIehJfpS1R7+x4=

"@esbuild/linux-s390x@0.25.5":
  version "0.25.5"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-s390x/download/@esbuild/linux-s390x-0.25.5.tgz#5669af81327a398a336d7e40e320b5bbd6e6e72d"
  integrity sha1-VmmvgTJ6OYozbX5A4yC1u9bm5y0=

"@esbuild/linux-x64@0.25.5":
  version "0.25.5"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-x64/download/@esbuild/linux-x64-0.25.5.tgz#b2357dd153aa49038967ddc1ffd90c68a9d2a0d4"
  integrity sha1-sjV90VOqSQOJZ93B/9kMaKnSoNQ=

"@esbuild/netbsd-arm64@0.25.5":
  version "0.25.5"
  resolved "http://r.npm.sankuai.com/@esbuild/netbsd-arm64/download/@esbuild/netbsd-arm64-0.25.5.tgz#53b4dfb8fe1cee93777c9e366893bd3daa6ba63d"
  integrity sha1-U7TfuP4c7pN3fJ42aJO9Paprpj0=

"@esbuild/netbsd-x64@0.25.5":
  version "0.25.4"
  resolved "http://r.npm.sankuai.com/@esbuild/netbsd-x64/download/@esbuild/netbsd-x64-0.25.4.tgz#ec401fb0b1ed0ac01d978564c5fc8634ed1dc2ed"
  integrity sha1-7EAfsLHtCsAdl4VkxfyGNO0dwu0=

"@esbuild/openbsd-arm64@0.25.5":
  version "0.25.5"
  resolved "http://r.npm.sankuai.com/@esbuild/openbsd-arm64/download/@esbuild/openbsd-arm64-0.25.5.tgz#2a796c87c44e8de78001d808c77d948a21ec22fd"
  integrity sha1-Knlsh8ROjeeAAdgIx32UiiHsIv0=

"@esbuild/openbsd-x64@0.25.5":
  version "0.25.4"
  resolved "http://r.npm.sankuai.com/@esbuild/openbsd-x64/download/@esbuild/openbsd-x64-0.25.4.tgz#2e25950bc10fa9db1e5c868e3d50c44f7c150fd7"
  integrity sha1-LiWVC8EPqdseXIaOPVDET3wVD9c=

"@esbuild/sunos-x64@0.25.5":
  version "0.25.5"
  resolved "http://r.npm.sankuai.com/@esbuild/sunos-x64/download/@esbuild/sunos-x64-0.25.5.tgz#a28164f5b997e8247d407e36c90d3fd5ddbe0dc5"
  integrity sha1-ooFk9bmX6CR9QH42yQ0/1d2+DcU=

"@esbuild/win32-arm64@0.25.5":
  version "0.25.4"
  resolved "http://r.npm.sankuai.com/@esbuild/win32-arm64/download/@esbuild/win32-arm64-0.25.4.tgz#b4dbcb57b21eeaf8331e424c3999b89d8951dc88"
  integrity sha1-tNvLV7Ie6vgzHkJMOZm4nYlR3Ig=

"@esbuild/win32-ia32@0.25.5":
  version "0.25.4"
  resolved "http://r.npm.sankuai.com/@esbuild/win32-ia32/download/@esbuild/win32-ia32-0.25.4.tgz#410842e5d66d4ece1757634e297a87635eb82f7a"
  integrity sha1-QQhC5dZtTs4XV2NOKXqHY164L3o=

"@esbuild/win32-x64@0.25.5":
  version "0.25.4"
  resolved "http://r.npm.sankuai.com/@esbuild/win32-x64/download/@esbuild/win32-x64-0.25.4.tgz#0b17ec8a70b2385827d52314c1253160a0b9bacc"
  integrity sha1-CxfsinCyOFgn1SMUwSUxYKC5usw=

"@eslint-community/eslint-utils@^4.2.0", "@eslint-community/eslint-utils@^4.4.0", "@eslint-community/eslint-utils@^4.7.0":
  version "4.7.0"
  resolved "http://r.npm.sankuai.com/@eslint-community/eslint-utils/download/@eslint-community/eslint-utils-4.7.0.tgz#607084630c6c033992a082de6e6fbc1a8b52175a"
  integrity sha1-YHCEYwxsAzmSoILebm+8GotSF1o=
  dependencies:
    eslint-visitor-keys "^3.4.3"

"@eslint-community/regexpp@^4.10.0", "@eslint-community/regexpp@^4.12.1":
  version "4.12.1"
  resolved "http://r.npm.sankuai.com/@eslint-community/regexpp/download/@eslint-community/regexpp-4.12.1.tgz#cfc6cffe39df390a3841cde2abccf92eaa7ae0e0"
  integrity sha1-z8bP/jnfOQo4Qc3iq8z5Lqp64OA=

"@eslint/config-array@^0.20.0":
  version "0.20.0"
  resolved "http://r.npm.sankuai.com/@eslint/config-array/download/@eslint/config-array-0.20.0.tgz#7a1232e82376712d3340012a2f561a2764d1988f"
  integrity sha1-ehIy6CN2cS0zQAEqL1YaJ2TRmI8=
  dependencies:
    "@eslint/object-schema" "^2.1.6"
    debug "^4.3.1"
    minimatch "^3.1.2"

"@eslint/config-helpers@^0.2.1":
  version "0.2.2"
  resolved "http://r.npm.sankuai.com/@eslint/config-helpers/download/@eslint/config-helpers-0.2.2.tgz#3779f76b894de3a8ec4763b79660e6d54d5b1010"
  integrity sha1-N3n3a4lN46jsR2O3lmDm1U1bEBA=

"@eslint/core@^0.14.0":
  version "0.14.0"
  resolved "http://r.npm.sankuai.com/@eslint/core/download/@eslint/core-0.14.0.tgz#326289380968eaf7e96f364e1e4cf8f3adf2d003"
  integrity sha1-MmKJOAlo6vfpbzZOHkz4863y0AM=
  dependencies:
    "@types/json-schema" "^7.0.15"

"@eslint/eslintrc@^3.3.1":
  version "3.3.1"
  resolved "http://r.npm.sankuai.com/@eslint/eslintrc/download/@eslint/eslintrc-3.3.1.tgz#e55f7f1dd400600dd066dbba349c4c0bac916964"
  integrity sha1-5V9/HdQAYA3QZtu6NJxMC6yRaWQ=
  dependencies:
    ajv "^6.12.4"
    debug "^4.3.2"
    espree "^10.0.1"
    globals "^14.0.0"
    ignore "^5.2.0"
    import-fresh "^3.2.1"
    js-yaml "^4.1.0"
    minimatch "^3.1.2"
    strip-json-comments "^3.1.1"

"@eslint/js@9.27.0":
  version "9.27.0"
  resolved "http://r.npm.sankuai.com/@eslint/js/download/@eslint/js-9.27.0.tgz#181a23460877c484f6dd03890f4e3fa2fdeb8ff0"
  integrity sha1-GBojRgh3xIT23QOJD04/ov3rj/A=

"@eslint/object-schema@^2.1.6":
  version "2.1.6"
  resolved "http://r.npm.sankuai.com/@eslint/object-schema/download/@eslint/object-schema-2.1.6.tgz#58369ab5b5b3ca117880c0f6c0b0f32f6950f24f"
  integrity sha1-WDaatbWzyhF4gMD2wLDzL2lQ8k8=

"@eslint/plugin-kit@^0.3.1":
  version "0.3.1"
  resolved "http://r.npm.sankuai.com/@eslint/plugin-kit/download/@eslint/plugin-kit-0.3.1.tgz#b71b037b2d4d68396df04a8c35a49481e5593067"
  integrity sha1-txsDey1NaDlt8EqMNaSUgeVZMGc=
  dependencies:
    "@eslint/core" "^0.14.0"
    levn "^0.4.1"

"@humanfs/core@^0.19.1":
  version "0.19.1"
  resolved "http://r.npm.sankuai.com/@humanfs/core/download/@humanfs/core-0.19.1.tgz#17c55ca7d426733fe3c561906b8173c336b40a77"
  integrity sha1-F8Vcp9Qmcz/jxWGQa4Fzwza0Cnc=

"@humanfs/node@^0.16.6":
  version "0.16.6"
  resolved "http://r.npm.sankuai.com/@humanfs/node/download/@humanfs/node-0.16.6.tgz#ee2a10eaabd1131987bf0488fd9b820174cd765e"
  integrity sha1-7ioQ6qvRExmHvwSI/ZuCAXTNdl4=
  dependencies:
    "@humanfs/core" "^0.19.1"
    "@humanwhocodes/retry" "^0.3.0"

"@humanwhocodes/module-importer@^1.0.1":
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/@humanwhocodes/module-importer/download/@humanwhocodes/module-importer-1.0.1.tgz#af5b2691a22b44be847b0ca81641c5fb6ad0172c"
  integrity sha1-r1smkaIrRL6EewyoFkHF+2rQFyw=

"@humanwhocodes/retry@^0.3.0":
  version "0.3.1"
  resolved "http://r.npm.sankuai.com/@humanwhocodes/retry/download/@humanwhocodes/retry-0.3.1.tgz#c72a5c76a9fbaf3488e231b13dc52c0da7bab42a"
  integrity sha1-xypcdqn7rzSI4jGxPcUsDae6tCo=

"@humanwhocodes/retry@^0.4.2":
  version "0.4.3"
  resolved "http://r.npm.sankuai.com/@humanwhocodes/retry/download/@humanwhocodes/retry-0.4.3.tgz#c2b9d2e374ee62c586d3adbea87199b1d7a7a6ba"
  integrity sha1-wrnS43TuYsWG062+qHGZsdenpro=

"@isaacs/cliui@^8.0.2":
  version "8.0.2"
  resolved "http://r.npm.sankuai.com/@isaacs/cliui/download/@isaacs/cliui-8.0.2.tgz#b37667b7bc181c168782259bab42474fbf52b550"
  integrity sha1-s3Znt7wYHBaHgiWbq0JHT79StVA=
  dependencies:
    string-width "^5.1.2"
    string-width-cjs "npm:string-width@^4.2.0"
    strip-ansi "^7.0.1"
    strip-ansi-cjs "npm:strip-ansi@^6.0.1"
    wrap-ansi "^8.1.0"
    wrap-ansi-cjs "npm:wrap-ansi@^7.0.0"

"@jridgewell/gen-mapping@^0.3.5":
  version "0.3.8"
  resolved "http://r.npm.sankuai.com/@jridgewell/gen-mapping/download/@jridgewell/gen-mapping-0.3.8.tgz#4f0e06362e01362f823d348f1872b08f666d8142"
  integrity sha1-Tw4GNi4BNi+CPTSPGHKwj2ZtgUI=
  dependencies:
    "@jridgewell/set-array" "^1.2.1"
    "@jridgewell/sourcemap-codec" "^1.4.10"
    "@jridgewell/trace-mapping" "^0.3.24"

"@jridgewell/resolve-uri@^3.1.0":
  version "3.1.2"
  resolved "http://r.npm.sankuai.com/@jridgewell/resolve-uri/download/@jridgewell/resolve-uri-3.1.2.tgz#7a0ee601f60f99a20c7c7c5ff0c80388c1189bd6"
  integrity sha1-eg7mAfYPmaIMfHxf8MgDiMEYm9Y=

"@jridgewell/set-array@^1.2.1":
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/@jridgewell/set-array/download/@jridgewell/set-array-1.2.1.tgz#558fb6472ed16a4c850b889530e6b36438c49280"
  integrity sha1-VY+2Ry7RakyFC4iVMOazZDjEkoA=

"@jridgewell/sourcemap-codec@^1.4.10", "@jridgewell/sourcemap-codec@^1.4.14", "@jridgewell/sourcemap-codec@^1.5.0":
  version "1.5.0"
  resolved "http://r.npm.sankuai.com/@jridgewell/sourcemap-codec/download/@jridgewell/sourcemap-codec-1.5.0.tgz#3188bcb273a414b0d215fd22a58540b989b9409a"
  integrity sha1-MYi8snOkFLDSFf0ipYVAuYm5QJo=

"@jridgewell/trace-mapping@^0.3.24", "@jridgewell/trace-mapping@^0.3.25":
  version "0.3.25"
  resolved "http://r.npm.sankuai.com/@jridgewell/trace-mapping/download/@jridgewell/trace-mapping-0.3.25.tgz#15f190e98895f3fc23276ee14bc76b675c2e50f0"
  integrity sha1-FfGQ6YiV8/wjJ27hS8drZ1wuUPA=
  dependencies:
    "@jridgewell/resolve-uri" "^3.1.0"
    "@jridgewell/sourcemap-codec" "^1.4.14"

"@nodelib/fs.scandir@2.1.5":
  version "2.1.5"
  resolved "http://r.npm.sankuai.com/@nodelib/fs.scandir/download/@nodelib/fs.scandir-2.1.5.tgz#7619c2eb21b25483f6d167548b4cfd5a7488c3d5"
  integrity sha1-dhnC6yGyVIP20WdUi0z9WnSIw9U=
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    run-parallel "^1.1.9"

"@nodelib/fs.stat@2.0.5", "@nodelib/fs.stat@^2.0.2":
  version "2.0.5"
  resolved "http://r.npm.sankuai.com/@nodelib/fs.stat/download/@nodelib/fs.stat-2.0.5.tgz#5bd262af94e9d25bd1e71b05deed44876a222e8b"
  integrity sha1-W9Jir5Tp0lvR5xsF3u1Eh2oiLos=

"@nodelib/fs.walk@^1.2.3":
  version "1.2.8"
  resolved "http://r.npm.sankuai.com/@nodelib/fs.walk/download/@nodelib/fs.walk-1.2.8.tgz#e95737e8bb6746ddedf69c556953494f196fe69a"
  integrity sha1-6Vc36LtnRt3t9pxVaVNJTxlv5po=
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    fastq "^1.6.0"

"@one-ini/wasm@0.1.1":
  version "0.1.1"
  resolved "http://r.npm.sankuai.com/@one-ini/wasm/download/@one-ini/wasm-0.1.1.tgz#6013659736c9dbfccc96e8a9c2b3de317df39323"
  integrity sha1-YBNllzbJ2/zMluipwrPeMX3zkyM=

"@oxlint/darwin-arm64@0.16.12":
  version "0.16.12"
  resolved "http://r.npm.sankuai.com/@oxlint/darwin-arm64/download/@oxlint/darwin-arm64-0.16.12.tgz#9ac93d9c64f017a63e889512c03c08082281d676"
  integrity sha1-msk9nGTwF6Y+iJUSwDwICCKB1nY=

"@oxlint/darwin-x64@0.16.12":
  version "0.16.12"
  resolved "http://r.npm.sankuai.com/@oxlint/darwin-x64/download/@oxlint/darwin-x64-0.16.12.tgz#73c9c08abdd643c4c1be2ef0cbd2a44b84a8de77"
  integrity sha1-c8nAir3WQ8TBvi7wy9KkS4So3nc=

"@oxlint/linux-arm64-gnu@0.16.12":
  version "0.16.12"
  resolved "http://r.npm.sankuai.com/@oxlint/linux-arm64-gnu/download/@oxlint/linux-arm64-gnu-0.16.12.tgz#5b5c600da7472e0b6bc83535c02769ff6073b1ed"
  integrity sha1-W1xgDadHLgtryDU1wCdp/2Bzse0=

"@oxlint/linux-arm64-musl@0.16.12":
  version "0.16.12"
  resolved "http://r.npm.sankuai.com/@oxlint/linux-arm64-musl/download/@oxlint/linux-arm64-musl-0.16.12.tgz#b422405d86358df5a06ee07897b65fdffbeef750"
  integrity sha1-tCJAXYY1jfWgbuB4l7Zf3/vu91A=

"@oxlint/linux-x64-gnu@0.16.12":
  version "0.16.12"
  resolved "http://r.npm.sankuai.com/@oxlint/linux-x64-gnu/download/@oxlint/linux-x64-gnu-0.16.12.tgz#0c00e2ec3ae83d11be1af2fe77f0ec30297f6171"
  integrity sha1-DADi7DroPRG+GvL+d/DsMCl/YXE=

"@oxlint/linux-x64-musl@0.16.12":
  version "0.16.12"
  resolved "http://r.npm.sankuai.com/@oxlint/linux-x64-musl/download/@oxlint/linux-x64-musl-0.16.12.tgz#0dd4ef01b4a6c3e8d6ac7489c07dc36574720ba8"
  integrity sha1-DdTvAbSmw+jWrHSJwH3DZXRyC6g=

"@oxlint/win32-arm64@0.16.12":
  version "0.16.12"
  resolved "http://r.npm.sankuai.com/@oxlint/win32-arm64/download/@oxlint/win32-arm64-0.16.12.tgz#d26abf017024c4786e19acf2829f476851b2d2fc"
  integrity sha1-0mq/AXAkxHhuGazygp9HaFGy0vw=

"@oxlint/win32-x64@0.16.12":
  version "0.16.12"
  resolved "http://r.npm.sankuai.com/@oxlint/win32-x64/download/@oxlint/win32-x64-0.16.12.tgz#b016ebee67409ba86f52320e1d8233781d9ddded"
  integrity sha1-sBbr7mdAm6hvUjIOHYIzeB2d3e0=

"@pkgjs/parseargs@^0.11.0":
  version "0.11.0"
  resolved "http://r.npm.sankuai.com/@pkgjs/parseargs/download/@pkgjs/parseargs-0.11.0.tgz#a77ea742fab25775145434eb1d2328cf5013ac33"
  integrity sha1-p36nQvqyV3UUVDTrHSMoz1ATrDM=

"@pkgr/core@^0.2.4":
  version "0.2.4"
  resolved "http://r.npm.sankuai.com/@pkgr/core/download/@pkgr/core-0.2.4.tgz#d897170a2b0ba51f78a099edccd968f7b103387c"
  integrity sha1-2JcXCisLpR94oJntzNlo97EDOHw=

"@playwright/test@^1.51.1":
  version "1.52.0"
  resolved "http://r.npm.sankuai.com/@playwright/test/download/@playwright/test-1.52.0.tgz#267ec595b43a8f4fa5e444ea503689629e91a5b8"
  integrity sha1-Jn7FlbQ6j0+l5ETqUDaJYp6Rpbg=
  dependencies:
    playwright "1.52.0"

"@polka/url@^1.0.0-next.24":
  version "1.0.0-next.29"
  resolved "http://r.npm.sankuai.com/@polka/url/download/@polka/url-1.0.0-next.29.tgz#5a40109a1ab5f84d6fd8fc928b19f367cbe7e7b1"
  integrity sha1-WkAQmhq1+E1v2PySixnzZ8vn57E=

"@rolldown/pluginutils@^1.0.0-beta.9":
  version "1.0.0-beta.9"
  resolved "http://r.npm.sankuai.com/@rolldown/pluginutils/download/@rolldown/pluginutils-1.0.0-beta.9.tgz#68ef9fff5a9791a642cea0dc4380ce6cb487a84a"
  integrity sha1-aO+f/1qXkaZCzqDcQ4DObLSHqEo=

"@rollup/pluginutils@^5.1.3":
  version "5.1.4"
  resolved "http://r.npm.sankuai.com/@rollup/pluginutils/download/@rollup/pluginutils-5.1.4.tgz#bb94f1f9eaaac944da237767cdfee6c5b2262d4a"
  integrity sha1-u5Tx+eqqyUTaI3dnzf7mxbImLUo=
  dependencies:
    "@types/estree" "^1.0.0"
    estree-walker "^2.0.2"
    picomatch "^4.0.2"

"@rollup/rollup-android-arm-eabi@4.41.1":
  version "4.41.1"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-android-arm-eabi/download/@rollup/rollup-android-arm-eabi-4.41.1.tgz#f39f09f60d4a562de727c960d7b202a2cf797424"
  integrity sha1-858J9g1KVi3nJ8lg17ICos95dCQ=

"@rollup/rollup-android-arm64@4.41.1":
  version "4.41.1"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-android-arm64/download/@rollup/rollup-android-arm64-4.41.1.tgz#d19af7e23760717f1d879d4ca3d2cd247742dff2"
  integrity sha1-0Zr34jdgcX8dh51Mo9LNJHdC3/I=

"@rollup/rollup-darwin-arm64@4.41.1":
  version "4.41.1"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-darwin-arm64/download/@rollup/rollup-darwin-arm64-4.41.1.tgz#1c3a2fbf205d80641728e05f4a56c909e95218b7"
  integrity sha1-HDovvyBdgGQXKOBfSlbJCelSGLc=

"@rollup/rollup-darwin-x64@4.41.1":
  version "4.41.1"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-darwin-x64/download/@rollup/rollup-darwin-x64-4.41.1.tgz#aa66d2ba1a25e609500e13bef06dc0e71cc0c0d4"
  integrity sha1-qmbSuhol5glQDhO+8G3A5xzAwNQ=

"@rollup/rollup-freebsd-arm64@4.41.1":
  version "4.41.1"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-freebsd-arm64/download/@rollup/rollup-freebsd-arm64-4.41.1.tgz#df10a7b6316a0ef1028c6ca71a081124c537e30d"
  integrity sha1-3xCntjFqDvECjGynGggRJMU34w0=

"@rollup/rollup-freebsd-x64@4.41.1":
  version "4.41.1"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-freebsd-x64/download/@rollup/rollup-freebsd-x64-4.41.1.tgz#a3fdce8a05e95b068cbcb46e4df5185e407d0c35"
  integrity sha1-o/3OigXpWwaMvLRuTfUYXkB9DDU=

"@rollup/rollup-linux-arm-gnueabihf@4.41.1":
  version "4.41.1"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-arm-gnueabihf/download/@rollup/rollup-linux-arm-gnueabihf-4.41.1.tgz#49f766c55383bd0498014a9d76924348c2f3890c"
  integrity sha1-SfdmxVODvQSYAUqddpJDSMLziQw=

"@rollup/rollup-linux-arm-musleabihf@4.41.1":
  version "4.41.1"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-arm-musleabihf/download/@rollup/rollup-linux-arm-musleabihf-4.41.1.tgz#1d4d7d32fc557e17d52e1857817381ea365e2959"
  integrity sha1-HU19MvxVfhfVLhhXgXOB6jZeKVk=

"@rollup/rollup-linux-arm64-gnu@4.41.1":
  version "4.41.1"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-arm64-gnu/download/@rollup/rollup-linux-arm64-gnu-4.41.1.tgz#f4fc317268441e9589edad3be8f62b6c03009bc1"
  integrity sha1-9PwxcmhEHpWJ7a076PYrbAMAm8E=

"@rollup/rollup-linux-arm64-musl@4.41.1":
  version "4.41.1"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-arm64-musl/download/@rollup/rollup-linux-arm64-musl-4.41.1.tgz#63a1f1b0671cb17822dabae827fef0e443aebeb7"
  integrity sha1-Y6HxsGccsXgi2rroJ/7w5EOuvrc=

"@rollup/rollup-linux-loongarch64-gnu@4.41.1":
  version "4.41.1"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-loongarch64-gnu/download/@rollup/rollup-linux-loongarch64-gnu-4.41.1.tgz#c659b01cc6c0730b547571fc3973e1e955369f98"
  integrity sha1-xlmwHMbAcwtUdXH8OXPh6VU2n5g=

"@rollup/rollup-linux-powerpc64le-gnu@4.41.1":
  version "4.41.1"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-powerpc64le-gnu/download/@rollup/rollup-linux-powerpc64le-gnu-4.41.1.tgz#612e746f9ad7e58480f964d65e0d6c3f4aae69a8"
  integrity sha1-YS50b5rX5YSA+WTWXg1sP0quaag=

"@rollup/rollup-linux-riscv64-gnu@4.41.1":
  version "4.41.1"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-riscv64-gnu/download/@rollup/rollup-linux-riscv64-gnu-4.41.1.tgz#4610dbd1dcfbbae32fbc10c20ae7387acb31110c"
  integrity sha1-RhDb0dz7uuMvvBDCCuc4essxEQw=

"@rollup/rollup-linux-riscv64-musl@4.41.1":
  version "4.41.1"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-riscv64-musl/download/@rollup/rollup-linux-riscv64-musl-4.41.1.tgz#054911fab40dc83fafc21e470193c058108f19d8"
  integrity sha1-BUkR+rQNyD+vwh5HAZPAWBCPGdg=

"@rollup/rollup-linux-s390x-gnu@4.41.1":
  version "4.41.1"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-s390x-gnu/download/@rollup/rollup-linux-s390x-gnu-4.41.1.tgz#98896eca8012547c7f04bd07eaa6896825f9e1a5"
  integrity sha1-mIluyoASVHx/BL0H6qaJaCX54aU=

"@rollup/rollup-linux-x64-gnu@4.41.1":
  version "4.41.1"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-x64-gnu/download/@rollup/rollup-linux-x64-gnu-4.41.1.tgz#01cf56844a1e636ee80dfb364e72c2b7142ad896"
  integrity sha1-Ac9WhEoeY27oDfs2TnLCtxQq2JY=

"@rollup/rollup-linux-x64-musl@4.41.1":
  version "4.41.1"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-x64-musl/download/@rollup/rollup-linux-x64-musl-4.41.1.tgz#e67c7531df6dff0b4c241101d4096617fbca87c3"
  integrity sha1-5nx1Md9t/wtMJBEB1AlmF/vKh8M=

"@rollup/rollup-win32-arm64-msvc@4.41.1":
  version "4.41.1"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-win32-arm64-msvc/download/@rollup/rollup-win32-arm64-msvc-4.41.1.tgz#7eeada98444e580674de6989284e4baacd48ea65"
  integrity sha1-furamEROWAZ03mmJKE5Lqs1I6mU=

"@rollup/rollup-win32-ia32-msvc@4.41.1":
  version "4.41.1"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-win32-ia32-msvc/download/@rollup/rollup-win32-ia32-msvc-4.41.1.tgz#516c4b54f80587b4a390aaf4940b40870271d35d"
  integrity sha1-UWxLVPgFh7SjkKr0lAtAhwJx010=

"@rollup/rollup-win32-x64-msvc@4.41.1":
  version "4.41.1"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-win32-x64-msvc/download/@rollup/rollup-win32-x64-msvc-4.41.1.tgz#848f99b0d9936d92221bb6070baeff4db6947a30"
  integrity sha1-hI+ZsNmTbZIiG7YHC67/TbaUejA=

"@sec-ant/readable-stream@^0.4.1":
  version "0.4.1"
  resolved "http://r.npm.sankuai.com/@sec-ant/readable-stream/download/@sec-ant/readable-stream-0.4.1.tgz#60de891bb126abfdc5410fdc6166aca065f10a0c"
  integrity sha1-YN6JG7Emq/3FQQ/cYWasoGXxCgw=

"@sindresorhus/merge-streams@^4.0.0":
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/@sindresorhus/merge-streams/download/@sindresorhus/merge-streams-4.0.0.tgz#abb11d99aeb6d27f1b563c38147a72d50058e339"
  integrity sha1-q7Edma620n8bVjw4FHpy1QBY4zk=

"@tsconfig/node22@^22.0.1":
  version "22.0.2"
  resolved "http://r.npm.sankuai.com/@tsconfig/node22/download/@tsconfig/node22-22.0.2.tgz#1e04e2c5cc946dac787d69bb502462a851ae51b6"
  integrity sha1-HgTixcyUbax4fWm7UCRiqFGuUbY=

"@types/estree@1.0.7", "@types/estree@^1.0.0", "@types/estree@^1.0.6":
  version "1.0.7"
  resolved "http://r.npm.sankuai.com/@types/estree/download/@types/estree-1.0.7.tgz#4158d3105276773d5b7695cd4834b1722e4f37a8"
  integrity sha1-QVjTEFJ2dz1bdpXNSDSxci5PN6g=

"@types/jsdom@^21.1.7":
  version "21.1.7"
  resolved "http://r.npm.sankuai.com/@types/jsdom/download/@types/jsdom-21.1.7.tgz#9edcb09e0b07ce876e7833922d3274149c898cfa"
  integrity sha1-ntywngsHzodueDOSLTJ0FJyJjPo=
  dependencies:
    "@types/node" "*"
    "@types/tough-cookie" "*"
    parse5 "^7.0.0"

"@types/json-schema@^7.0.15":
  version "7.0.15"
  resolved "http://r.npm.sankuai.com/@types/json-schema/download/@types/json-schema-7.0.15.tgz#596a1747233694d50f6ad8a7869fcb6f56cf5841"
  integrity sha1-WWoXRyM2lNUPatinhp/Lb1bPWEE=

"@types/node@*", "@types/node@^22.14.0":
  version "22.15.23"
  resolved "http://r.npm.sankuai.com/@types/node/download/@types/node-22.15.23.tgz#a0b7c03f951f1ffe381a6a345c68d80e48043dd0"
  integrity sha1-oLfAP5UfH/44Gmo0XGjYDkgEPdA=
  dependencies:
    undici-types "~6.21.0"

"@types/tough-cookie@*":
  version "4.0.5"
  resolved "http://r.npm.sankuai.com/@types/tough-cookie/download/@types/tough-cookie-4.0.5.tgz#cb6e2a691b70cb177c6e3ae9c1d2e8b2ea8cd304"
  integrity sha1-y24qaRtwyxd8bjrpwdLosuqM0wQ=

"@typescript-eslint/eslint-plugin@8.33.0":
  version "8.33.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/eslint-plugin/download/@typescript-eslint/eslint-plugin-8.33.0.tgz#51ed03649575ba51bcee7efdbfd85283249b5447"
  integrity sha1-Ue0DZJV1ulG87n79v9hSgySbVEc=
  dependencies:
    "@eslint-community/regexpp" "^4.10.0"
    "@typescript-eslint/scope-manager" "8.33.0"
    "@typescript-eslint/type-utils" "8.33.0"
    "@typescript-eslint/utils" "8.33.0"
    "@typescript-eslint/visitor-keys" "8.33.0"
    graphemer "^1.4.0"
    ignore "^7.0.0"
    natural-compare "^1.4.0"
    ts-api-utils "^2.1.0"

"@typescript-eslint/parser@8.33.0":
  version "8.33.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/parser/download/@typescript-eslint/parser-8.33.0.tgz#8e523c2b447ad7cd6ac91b719d8b37449481784d"
  integrity sha1-jlI8K0R6181qyRtxnYs3RJSBeE0=
  dependencies:
    "@typescript-eslint/scope-manager" "8.33.0"
    "@typescript-eslint/types" "8.33.0"
    "@typescript-eslint/typescript-estree" "8.33.0"
    "@typescript-eslint/visitor-keys" "8.33.0"
    debug "^4.3.4"

"@typescript-eslint/project-service@8.33.0":
  version "8.33.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/project-service/download/@typescript-eslint/project-service-8.33.0.tgz#71f37ef9010de47bf20963914743c5cbef851e08"
  integrity sha1-cfN++QEN5HvyCWORR0PFy++FHgg=
  dependencies:
    "@typescript-eslint/tsconfig-utils" "^8.33.0"
    "@typescript-eslint/types" "^8.33.0"
    debug "^4.3.4"

"@typescript-eslint/scope-manager@8.33.0":
  version "8.33.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/scope-manager/download/@typescript-eslint/scope-manager-8.33.0.tgz#459cf0c49d410800b1a023b973c62d699b09bf4c"
  integrity sha1-RZzwxJ1BCACxoCO5c8YtaZsJv0w=
  dependencies:
    "@typescript-eslint/types" "8.33.0"
    "@typescript-eslint/visitor-keys" "8.33.0"

"@typescript-eslint/tsconfig-utils@8.33.0", "@typescript-eslint/tsconfig-utils@^8.33.0":
  version "8.33.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/tsconfig-utils/download/@typescript-eslint/tsconfig-utils-8.33.0.tgz#316adab038bbdc43e448781d5a816c2973eab73e"
  integrity sha1-MWrasDi73EPkSHgdWoFsKXPqtz4=

"@typescript-eslint/type-utils@8.33.0":
  version "8.33.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/type-utils/download/@typescript-eslint/type-utils-8.33.0.tgz#f06124b2d6db8a51b24990cb123c9543af93fef5"
  integrity sha1-8GEkstbbilGySZDLEjyVQ6+T/vU=
  dependencies:
    "@typescript-eslint/typescript-estree" "8.33.0"
    "@typescript-eslint/utils" "8.33.0"
    debug "^4.3.4"
    ts-api-utils "^2.1.0"

"@typescript-eslint/types@8.33.0", "@typescript-eslint/types@^8.33.0":
  version "8.33.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/types/download/@typescript-eslint/types-8.33.0.tgz#02a7dbba611a8abf1ad2a9e00f72f7b94b5ab0ee"
  integrity sha1-AqfbumEair8a0qngD3L3uUtasO4=

"@typescript-eslint/typescript-estree@8.33.0":
  version "8.33.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/typescript-estree/download/@typescript-eslint/typescript-estree-8.33.0.tgz#abcc1d3db75a8e9fd2e274ee8c4099fa2399abfd"
  integrity sha1-q8wdPbdajp/S4nTujECZ+iOZq/0=
  dependencies:
    "@typescript-eslint/project-service" "8.33.0"
    "@typescript-eslint/tsconfig-utils" "8.33.0"
    "@typescript-eslint/types" "8.33.0"
    "@typescript-eslint/visitor-keys" "8.33.0"
    debug "^4.3.4"
    fast-glob "^3.3.2"
    is-glob "^4.0.3"
    minimatch "^9.0.4"
    semver "^7.6.0"
    ts-api-utils "^2.1.0"

"@typescript-eslint/utils@8.33.0", "@typescript-eslint/utils@^8.24.0", "@typescript-eslint/utils@^8.26.0":
  version "8.33.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/utils/download/@typescript-eslint/utils-8.33.0.tgz#574ad5edee371077b9e28ca6fb804f2440f447c1"
  integrity sha1-V0rV7e43EHe54oym+4BPJED0R8E=
  dependencies:
    "@eslint-community/eslint-utils" "^4.7.0"
    "@typescript-eslint/scope-manager" "8.33.0"
    "@typescript-eslint/types" "8.33.0"
    "@typescript-eslint/typescript-estree" "8.33.0"

"@typescript-eslint/visitor-keys@8.33.0":
  version "8.33.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/visitor-keys/download/@typescript-eslint/visitor-keys-8.33.0.tgz#fbae16fd3594531f8cad95d421125d634e9974fe"
  integrity sha1-+64W/TWUUx+MrZXUIRJdY06ZdP4=
  dependencies:
    "@typescript-eslint/types" "8.33.0"
    eslint-visitor-keys "^4.2.0"

"@vitejs/plugin-vue-jsx@^4.1.2":
  version "4.2.0"
  resolved "http://r.npm.sankuai.com/@vitejs/plugin-vue-jsx/download/@vitejs/plugin-vue-jsx-4.2.0.tgz#2738ec05d4705ed553a107342017192e37351640"
  integrity sha1-JzjsBdRwXtVToQc0IBcZLjc1FkA=
  dependencies:
    "@babel/core" "^7.27.1"
    "@babel/plugin-transform-typescript" "^7.27.1"
    "@rolldown/pluginutils" "^1.0.0-beta.9"
    "@vue/babel-plugin-jsx" "^1.4.0"

"@vitejs/plugin-vue@^5.2.3":
  version "5.2.4"
  resolved "http://r.npm.sankuai.com/@vitejs/plugin-vue/download/@vitejs/plugin-vue-5.2.4.tgz#9e8a512eb174bfc2a333ba959bbf9de428d89ad8"
  integrity sha1-nopRLrF0v8KjM7qVm7+d5CjYmtg=

"@vitest/eslint-plugin@^1.1.39":
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/@vitest/eslint-plugin/download/@vitest/eslint-plugin-1.2.1.tgz#99136de056721e37d1b02832dbe88a78b2620b70"
  integrity sha1-mRNt4FZyHjfRsCgy2+iKeLJiC3A=
  dependencies:
    "@typescript-eslint/utils" "^8.24.0"

"@vitest/expect@3.1.4":
  version "3.1.4"
  resolved "http://r.npm.sankuai.com/@vitest/expect/download/@vitest/expect-3.1.4.tgz#837651a71682e3611c3df7b58b157ba485bd8029"
  integrity sha1-g3ZRpxaC42EcPfe1ixV7pIW9gCk=
  dependencies:
    "@vitest/spy" "3.1.4"
    "@vitest/utils" "3.1.4"
    chai "^5.2.0"
    tinyrainbow "^2.0.0"

"@vitest/mocker@3.1.4":
  version "3.1.4"
  resolved "http://r.npm.sankuai.com/@vitest/mocker/download/@vitest/mocker-3.1.4.tgz#73441022b86c7299bfbd11a9fb2e99a7ddc2bb0e"
  integrity sha1-c0QQIrhscpm/vRGp+y6Zp93Cuw4=
  dependencies:
    "@vitest/spy" "3.1.4"
    estree-walker "^3.0.3"
    magic-string "^0.30.17"

"@vitest/pretty-format@3.1.4", "@vitest/pretty-format@^3.1.4":
  version "3.1.4"
  resolved "http://r.npm.sankuai.com/@vitest/pretty-format/download/@vitest/pretty-format-3.1.4.tgz#da3e98c250cde3ce39fe8e709339814607b185e8"
  integrity sha1-2j6YwlDN4845/o5wkzmBRgexheg=
  dependencies:
    tinyrainbow "^2.0.0"

"@vitest/runner@3.1.4":
  version "3.1.4"
  resolved "http://r.npm.sankuai.com/@vitest/runner/download/@vitest/runner-3.1.4.tgz#19fa16eb397f5325b99baca48c2bca6cadd098fa"
  integrity sha1-GfoW6zl/UyW5m6ykjCvKbK3QmPo=
  dependencies:
    "@vitest/utils" "3.1.4"
    pathe "^2.0.3"

"@vitest/snapshot@3.1.4":
  version "3.1.4"
  resolved "http://r.npm.sankuai.com/@vitest/snapshot/download/@vitest/snapshot-3.1.4.tgz#7897d4960a3cf617fb0f17e182cc15c7e3e4ed3f"
  integrity sha1-eJfUlgo89hf7DxfhgswVx+Pk7T8=
  dependencies:
    "@vitest/pretty-format" "3.1.4"
    magic-string "^0.30.17"
    pathe "^2.0.3"

"@vitest/spy@3.1.4":
  version "3.1.4"
  resolved "http://r.npm.sankuai.com/@vitest/spy/download/@vitest/spy-3.1.4.tgz#94bb566da7ef6deb7c4e1fd79b78f19aa5465b9f"
  integrity sha1-lLtWbafvbet8Th/Xm3jxmqVGW58=
  dependencies:
    tinyspy "^3.0.2"

"@vitest/utils@3.1.4":
  version "3.1.4"
  resolved "http://r.npm.sankuai.com/@vitest/utils/download/@vitest/utils-3.1.4.tgz#f9f20d92f1384a9d66548c480885390760047b5e"
  integrity sha1-+fINkvE4Sp1mVIxICIU5B2AEe14=
  dependencies:
    "@vitest/pretty-format" "3.1.4"
    loupe "^3.1.3"
    tinyrainbow "^2.0.0"

"@volar/language-core@2.4.14", "@volar/language-core@~2.4.11":
  version "2.4.14"
  resolved "http://r.npm.sankuai.com/@volar/language-core/download/@volar/language-core-2.4.14.tgz#dac7573014d4f3bafb186cb16888ffea5698be71"
  integrity sha1-2sdXMBTU87r7GGyxaIj/6laYvnE=
  dependencies:
    "@volar/source-map" "2.4.14"

"@volar/source-map@2.4.14":
  version "2.4.14"
  resolved "http://r.npm.sankuai.com/@volar/source-map/download/@volar/source-map-2.4.14.tgz#cdcecd533c2e767449b2414cc22327d2bda7ef95"
  integrity sha1-zc7NUzwudnRJskFMwiMn0r2n75U=

"@volar/typescript@~2.4.11":
  version "2.4.14"
  resolved "http://r.npm.sankuai.com/@volar/typescript/download/@volar/typescript-2.4.14.tgz#b99a1025dd6a8b751e96627ebcb0739ceed0e5f1"
  integrity sha1-uZoQJd1qi3UelmJ+vLBznO7Q5fE=
  dependencies:
    "@volar/language-core" "2.4.14"
    path-browserify "^1.0.1"
    vscode-uri "^3.0.8"

"@vue/babel-helper-vue-transform-on@1.4.0":
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/@vue/babel-helper-vue-transform-on/download/@vue/babel-helper-vue-transform-on-1.4.0.tgz#616020488692a9c42a613280d62ed1b727045d95"
  integrity sha1-YWAgSIaSqcQqYTKA1i7RtycEXZU=

"@vue/babel-plugin-jsx@^1.1.5", "@vue/babel-plugin-jsx@^1.4.0":
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/@vue/babel-plugin-jsx/download/@vue/babel-plugin-jsx-1.4.0.tgz#c155c795ce980edf46aa6feceed93945a95ca658"
  integrity sha1-wVXHlc6YDt9Gqm/s7tk5Ralcplg=
  dependencies:
    "@babel/helper-module-imports" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.26.5"
    "@babel/plugin-syntax-jsx" "^7.25.9"
    "@babel/template" "^7.26.9"
    "@babel/traverse" "^7.26.9"
    "@babel/types" "^7.26.9"
    "@vue/babel-helper-vue-transform-on" "1.4.0"
    "@vue/babel-plugin-resolve-type" "1.4.0"
    "@vue/shared" "^3.5.13"

"@vue/babel-plugin-resolve-type@1.4.0":
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/@vue/babel-plugin-resolve-type/download/@vue/babel-plugin-resolve-type-1.4.0.tgz#4d357a81fb0cc9cad0e8c81b118115bda2c51543"
  integrity sha1-TTV6gfsMycrQ6MgbEYEVvaLFFUM=
  dependencies:
    "@babel/code-frame" "^7.26.2"
    "@babel/helper-module-imports" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.26.5"
    "@babel/parser" "^7.26.9"
    "@vue/compiler-sfc" "^3.5.13"

"@vue/compiler-core@3.5.15":
  version "3.5.15"
  resolved "http://r.npm.sankuai.com/@vue/compiler-core/download/@vue/compiler-core-3.5.15.tgz#7cbda69429490f4ec0f68126dd0d6eae050dd01c"
  integrity sha1-fL2mlClJD07A9oEm3Q1urgUN0Bw=
  dependencies:
    "@babel/parser" "^7.27.2"
    "@vue/shared" "3.5.15"
    entities "^4.5.0"
    estree-walker "^2.0.2"
    source-map-js "^1.2.1"

"@vue/compiler-dom@3.5.15", "@vue/compiler-dom@^3.3.4", "@vue/compiler-dom@^3.5.0":
  version "3.5.15"
  resolved "http://r.npm.sankuai.com/@vue/compiler-dom/download/@vue/compiler-dom-3.5.15.tgz#2de7fec1a685c236585a4a1fb12cc586d7f0ffef"
  integrity sha1-Lef+waaFwjZYWkofsSzFhtfw/+8=
  dependencies:
    "@vue/compiler-core" "3.5.15"
    "@vue/shared" "3.5.15"

"@vue/compiler-sfc@3.5.15", "@vue/compiler-sfc@^3.5.13":
  version "3.5.15"
  resolved "http://r.npm.sankuai.com/@vue/compiler-sfc/download/@vue/compiler-sfc-3.5.15.tgz#d45a5c5171d8823c59725d31af406693c8816195"
  integrity sha1-1FpcUXHYgjxZcl0xr0Bmk8iBYZU=
  dependencies:
    "@babel/parser" "^7.27.2"
    "@vue/compiler-core" "3.5.15"
    "@vue/compiler-dom" "3.5.15"
    "@vue/compiler-ssr" "3.5.15"
    "@vue/shared" "3.5.15"
    estree-walker "^2.0.2"
    magic-string "^0.30.17"
    postcss "^8.5.3"
    source-map-js "^1.2.1"

"@vue/compiler-ssr@3.5.15":
  version "3.5.15"
  resolved "http://r.npm.sankuai.com/@vue/compiler-ssr/download/@vue/compiler-ssr-3.5.15.tgz#b6ffbb19ff7126fc0e60cbf9c4ca34d9a9ce9fb2"
  integrity sha1-tv+7Gf9xJvwOYMv5xMo02anOn7I=
  dependencies:
    "@vue/compiler-dom" "3.5.15"
    "@vue/shared" "3.5.15"

"@vue/compiler-vue2@^2.7.16":
  version "2.7.16"
  resolved "http://r.npm.sankuai.com/@vue/compiler-vue2/download/@vue/compiler-vue2-2.7.16.tgz#2ba837cbd3f1b33c2bc865fbe1a3b53fb611e249"
  integrity sha1-K6g3y9PxszwryGX74aO1P7YR4kk=
  dependencies:
    de-indent "^1.0.2"
    he "^1.2.0"

"@vue/devtools-api@^6.6.4":
  version "6.6.4"
  resolved "http://r.npm.sankuai.com/@vue/devtools-api/download/@vue/devtools-api-6.6.4.tgz#cbe97fe0162b365edc1dba80e173f90492535343"
  integrity sha1-y+l/4BYrNl7cHbqA4XP5BJJTU0M=

"@vue/devtools-api@^7.7.2":
  version "7.7.6"
  resolved "http://r.npm.sankuai.com/@vue/devtools-api/download/@vue/devtools-api-7.7.6.tgz#4af5dbc77bcc8543f0a8e6f029f598ed978d6c7d"
  integrity sha1-SvXbx3vMhUPwqObwKfWY7ZeNbH0=
  dependencies:
    "@vue/devtools-kit" "^7.7.6"

"@vue/devtools-core@^7.7.6":
  version "7.7.6"
  resolved "http://r.npm.sankuai.com/@vue/devtools-core/download/@vue/devtools-core-7.7.6.tgz#7e2ef93c05af809e5ed159ffc1b910f030976b83"
  integrity sha1-fi75PAWvgJ5e0Vn/wbkQ8DCXa4M=
  dependencies:
    "@vue/devtools-kit" "^7.7.6"
    "@vue/devtools-shared" "^7.7.6"
    mitt "^3.0.1"
    nanoid "^5.1.0"
    pathe "^2.0.3"
    vite-hot-client "^2.0.4"

"@vue/devtools-kit@^7.7.6":
  version "7.7.6"
  resolved "http://r.npm.sankuai.com/@vue/devtools-kit/download/@vue/devtools-kit-7.7.6.tgz#3d9cbe2378a65ed7c4baa77ecc0f7ecdfd185fbb"
  integrity sha1-PZy+I3imXtfEuqd+zA9+zf0YX7s=
  dependencies:
    "@vue/devtools-shared" "^7.7.6"
    birpc "^2.3.0"
    hookable "^5.5.3"
    mitt "^3.0.1"
    perfect-debounce "^1.0.0"
    speakingurl "^14.0.1"
    superjson "^2.2.2"

"@vue/devtools-shared@^7.7.6":
  version "7.7.6"
  resolved "http://r.npm.sankuai.com/@vue/devtools-shared/download/@vue/devtools-shared-7.7.6.tgz#5da2218df61b605b7b88e725241fc6640df0e4b5"
  integrity sha1-XaIhjfYbYFt7iOclJB/GZA3w5LU=
  dependencies:
    rfdc "^1.4.1"

"@vue/eslint-config-prettier@^10.2.0":
  version "10.2.0"
  resolved "http://r.npm.sankuai.com/@vue/eslint-config-prettier/download/@vue/eslint-config-prettier-10.2.0.tgz#49a5ed571acb81820a216e6d88ebf1f3def321d0"
  integrity sha1-SaXtVxrLgYIKIW5tiOvx897zIdA=
  dependencies:
    eslint-config-prettier "^10.0.1"
    eslint-plugin-prettier "^5.2.2"

"@vue/eslint-config-typescript@^14.5.0":
  version "14.5.0"
  resolved "http://r.npm.sankuai.com/@vue/eslint-config-typescript/download/@vue/eslint-config-typescript-14.5.0.tgz#1fdce9f2ad8fc114d3209a4a2009e2605a26abb3"
  integrity sha1-H9zp8q2PwRTTIJpKIAniYFomq7M=
  dependencies:
    "@typescript-eslint/utils" "^8.26.0"
    fast-glob "^3.3.3"
    typescript-eslint "^8.26.0"
    vue-eslint-parser "^10.1.1"

"@vue/language-core@2.2.10":
  version "2.2.10"
  resolved "http://r.npm.sankuai.com/@vue/language-core/download/@vue/language-core-2.2.10.tgz#5ae1e71a4e16dd59d1e4bac167f4b9c8c04d9f17"
  integrity sha1-WuHnGk4W3VnR5LrBZ/S5yMBNnxc=
  dependencies:
    "@volar/language-core" "~2.4.11"
    "@vue/compiler-dom" "^3.5.0"
    "@vue/compiler-vue2" "^2.7.16"
    "@vue/shared" "^3.5.0"
    alien-signals "^1.0.3"
    minimatch "^9.0.3"
    muggle-string "^0.4.1"
    path-browserify "^1.0.1"

"@vue/reactivity@3.5.15":
  version "3.5.15"
  resolved "http://r.npm.sankuai.com/@vue/reactivity/download/@vue/reactivity-3.5.15.tgz#18d00ae922e8b0dc09afe952bd47510c6e4305e9"
  integrity sha1-GNAK6SLosNwJr+lSvUdRDG5DBek=
  dependencies:
    "@vue/shared" "3.5.15"

"@vue/runtime-core@3.5.15":
  version "3.5.15"
  resolved "http://r.npm.sankuai.com/@vue/runtime-core/download/@vue/runtime-core-3.5.15.tgz#0accac4b441bb4aa00f58ba353b6f61e83f45202"
  integrity sha1-CsysS0QbtKoA9YujU7b2HoP0UgI=
  dependencies:
    "@vue/reactivity" "3.5.15"
    "@vue/shared" "3.5.15"

"@vue/runtime-dom@3.5.15":
  version "3.5.15"
  resolved "http://r.npm.sankuai.com/@vue/runtime-dom/download/@vue/runtime-dom-3.5.15.tgz#048b725069f6d3fe5002e53eba118466d1fbec84"
  integrity sha1-BItyUGn20/5QAuU+uhGEZtH77IQ=
  dependencies:
    "@vue/reactivity" "3.5.15"
    "@vue/runtime-core" "3.5.15"
    "@vue/shared" "3.5.15"
    csstype "^3.1.3"

"@vue/server-renderer@3.5.15":
  version "3.5.15"
  resolved "http://r.npm.sankuai.com/@vue/server-renderer/download/@vue/server-renderer-3.5.15.tgz#c1e8597e4bec8ea0b323c32dc032aa5c1f2983f4"
  integrity sha1-wehZfkvsjqCzI8MtwDKqXB8pg/Q=
  dependencies:
    "@vue/compiler-ssr" "3.5.15"
    "@vue/shared" "3.5.15"

"@vue/shared@3.5.15", "@vue/shared@^3.5.0", "@vue/shared@^3.5.13":
  version "3.5.15"
  resolved "http://r.npm.sankuai.com/@vue/shared/download/@vue/shared-3.5.15.tgz#4c633a0e66f38119e38eecf340b4a65b0bad7192"
  integrity sha1-TGM6DmbzgRnjjuzzQLSmWwutcZI=

"@vue/test-utils@^2.4.6":
  version "2.4.6"
  resolved "http://r.npm.sankuai.com/@vue/test-utils/download/@vue/test-utils-2.4.6.tgz#7d534e70c4319d2a587d6a3b45a39e9695ade03c"
  integrity sha1-fVNOcMQxnSpYfWo7RaOelpWt4Dw=
  dependencies:
    js-beautify "^1.14.9"
    vue-component-type-helpers "^2.0.0"

"@vue/tsconfig@^0.7.0":
  version "0.7.0"
  resolved "http://r.npm.sankuai.com/@vue/tsconfig/download/@vue/tsconfig-0.7.0.tgz#67044c847b7a137b8cbfd6b23104c36dbaf80d1d"
  integrity sha1-ZwRMhHt6E3uMv9ayMQTDbbr4DR0=

abbrev@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/abbrev/download/abbrev-2.0.0.tgz#cf59829b8b4f03f89dda2771cb7f3653828c89bf"
  integrity sha1-z1mCm4tPA/id2idxy382U4KMib8=

acorn-jsx@^5.3.2:
  version "5.3.2"
  resolved "http://r.npm.sankuai.com/acorn-jsx/download/acorn-jsx-5.3.2.tgz#7ed5bb55908b3b2f1bc55c6af1653bada7f07937"
  integrity sha1-ftW7VZCLOy8bxVxq8WU7rafweTc=

acorn@^8.14.0:
  version "8.14.1"
  resolved "http://r.npm.sankuai.com/acorn/download/acorn-8.14.1.tgz#721d5dc10f7d5b5609a891773d47731796935dfb"
  integrity sha1-ch1dwQ99W1YJqJF3PUdzF5aTXfs=

agent-base@^7.1.0, agent-base@^7.1.2:
  version "7.1.3"
  resolved "http://r.npm.sankuai.com/agent-base/download/agent-base-7.1.3.tgz#29435eb821bc4194633a5b89e5bc4703bafc25a1"
  integrity sha1-KUNeuCG8QZRjOluJ5bxHA7r8JaE=

ajv@^6.12.4:
  version "6.12.6"
  resolved "http://r.npm.sankuai.com/ajv/download/ajv-6.12.6.tgz#baf5a62e802b07d977034586f8c3baf5adf26df4"
  integrity sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

alien-signals@^1.0.3:
  version "1.0.13"
  resolved "http://r.npm.sankuai.com/alien-signals/download/alien-signals-1.0.13.tgz#8d6db73462f742ee6b89671fbd8c37d0b1727a7e"
  integrity sha1-jW23NGL3Qu5riWcfvYw30LFyen4=

ansi-regex@^5.0.1:
  version "5.0.1"
  resolved "http://r.npm.sankuai.com/ansi-regex/download/ansi-regex-5.0.1.tgz#082cb2c89c9fe8659a311a53bd6a4dc5301db304"
  integrity sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=

ansi-regex@^6.0.1:
  version "6.1.0"
  resolved "http://r.npm.sankuai.com/ansi-regex/download/ansi-regex-6.1.0.tgz#95ec409c69619d6cb1b8b34f14b660ef28ebd654"
  integrity sha1-lexAnGlhnWyxuLNPFLZg7yjr1lQ=

ansi-styles@^4.0.0, ansi-styles@^4.1.0:
  version "4.3.0"
  resolved "http://r.npm.sankuai.com/ansi-styles/download/ansi-styles-4.3.0.tgz#edd803628ae71c04c85ae7a0906edad34b648937"
  integrity sha1-7dgDYornHATIWuegkG7a00tkiTc=
  dependencies:
    color-convert "^2.0.1"

ansi-styles@^6.1.0, ansi-styles@^6.2.1:
  version "6.2.1"
  resolved "http://r.npm.sankuai.com/ansi-styles/download/ansi-styles-6.2.1.tgz#0e62320cf99c21afff3b3012192546aacbfb05c5"
  integrity sha1-DmIyDPmcIa//OzASGSVGqsv7BcU=

argparse@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/argparse/download/argparse-2.0.1.tgz#246f50f3ca78a3240f6c997e8a9bd1eac49e4b38"
  integrity sha1-JG9Q88p4oyQPbJl+ipvR6sSeSzg=

assertion-error@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/assertion-error/download/assertion-error-2.0.1.tgz#f641a196b335690b1070bf00b6e7593fec190bf7"
  integrity sha1-9kGhlrM1aQsQcL8AtudZP+wZC/c=

balanced-match@^1.0.0:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/balanced-match/download/balanced-match-1.0.2.tgz#e83e3a7e3f300b34cb9d87f615fa0cbf357690ee"
  integrity sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=

birpc@^2.3.0:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/birpc/download/birpc-2.3.0.tgz#e5a402dc785ef952a2383ef3cfc075e0842f3e8c"
  integrity sha1-5aQC3Hhe+VKiOD7zz8B14IQvPow=

boolbase@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/boolbase/download/boolbase-1.0.0.tgz#68dff5fbe60c51eb37725ea9e3ed310dcc1e776e"
  integrity sha1-aN/1++YMUes3cl6p4+0xDcwed24=

brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "http://r.npm.sankuai.com/brace-expansion/download/brace-expansion-1.1.11.tgz#3c7fcbf529d87226f3d2f52b966ff5271eb441dd"
  integrity sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

brace-expansion@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/brace-expansion/download/brace-expansion-2.0.1.tgz#1edc459e0f0c548486ecf9fc99f2221364b9a0ae"
  integrity sha1-HtxFng8MVISG7Pn8mfIiE2S5oK4=
  dependencies:
    balanced-match "^1.0.0"

braces@^3.0.3:
  version "3.0.3"
  resolved "http://r.npm.sankuai.com/braces/download/braces-3.0.3.tgz#490332f40919452272d55a8480adc0c441358789"
  integrity sha1-SQMy9AkZRSJy1VqEgK3AxEE1h4k=
  dependencies:
    fill-range "^7.1.1"

browserslist@^4.24.0:
  version "4.24.5"
  resolved "http://r.npm.sankuai.com/browserslist/download/browserslist-4.24.5.tgz#aa0f5b8560fe81fde84c6dcb38f759bafba0e11b"
  integrity sha1-qg9bhWD+gf3oTG3LOPdZuvug4Rs=
  dependencies:
    caniuse-lite "^1.0.30001716"
    electron-to-chromium "^1.5.149"
    node-releases "^2.0.19"
    update-browserslist-db "^1.1.3"

bundle-name@^4.1.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/bundle-name/download/bundle-name-4.1.0.tgz#f3b96b34160d6431a19d7688135af7cfb8797889"
  integrity sha1-87lrNBYNZDGhnXaIE1r3z7h5eIk=
  dependencies:
    run-applescript "^7.0.0"

cac@^6.7.14:
  version "6.7.14"
  resolved "http://r.npm.sankuai.com/cac/download/cac-6.7.14.tgz#804e1e6f506ee363cb0e3ccbb09cad5dd9870959"
  integrity sha1-gE4eb1Bu42PLDjzLsJytXdmHCVk=

callsites@^3.0.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/callsites/download/callsites-3.1.0.tgz#b3630abd8943432f54b3f0519238e33cd7df2f73"
  integrity sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=

caniuse-lite@^1.0.30001716:
  version "1.0.30001718"
  resolved "http://r.npm.sankuai.com/caniuse-lite/download/caniuse-lite-1.0.30001718.tgz#dae13a9c80d517c30c6197515a96131c194d8f82"
  integrity sha1-2uE6nIDVF8MMYZdRWpYTHBlNj4I=

chai@^5.2.0:
  version "5.2.0"
  resolved "http://r.npm.sankuai.com/chai/download/chai-5.2.0.tgz#1358ee106763624114addf84ab02697e411c9c05"
  integrity sha1-E1juEGdjYkEUrd+EqwJpfkEcnAU=
  dependencies:
    assertion-error "^2.0.1"
    check-error "^2.1.1"
    deep-eql "^5.0.1"
    loupe "^3.1.0"
    pathval "^2.0.0"

chalk@^4.0.0:
  version "4.1.2"
  resolved "http://r.npm.sankuai.com/chalk/download/chalk-4.1.2.tgz#aac4e2b7734a740867aeb16bf02aad556a1e7a01"
  integrity sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

check-error@^2.1.1:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/check-error/download/check-error-2.1.1.tgz#87eb876ae71ee388fa0471fe423f494be1d96ccc"
  integrity sha1-h+uHauce44j6BHH+Qj9JS+HZbMw=

color-convert@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/color-convert/download/color-convert-2.0.1.tgz#72d3a68d598c9bdb3af2ad1e84f21d896abd4de3"
  integrity sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=
  dependencies:
    color-name "~1.1.4"

color-name@~1.1.4:
  version "1.1.4"
  resolved "http://r.npm.sankuai.com/color-name/download/color-name-1.1.4.tgz#c2a09a87acbde69543de6f63fa3995c826c536a2"
  integrity sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=

commander@^10.0.0:
  version "10.0.1"
  resolved "http://r.npm.sankuai.com/commander/download/commander-10.0.1.tgz#881ee46b4f77d1c1dccc5823433aa39b022cbe06"
  integrity sha1-iB7ka0930cHczFgjQzqjmwIsvgY=

concat-map@0.0.1:
  version "0.0.1"
  resolved "http://r.npm.sankuai.com/concat-map/download/concat-map-0.0.1.tgz#d8a96bd77fd68df7793a73036a3ba0d5405d477b"
  integrity sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=

config-chain@^1.1.13:
  version "1.1.13"
  resolved "http://r.npm.sankuai.com/config-chain/download/config-chain-1.1.13.tgz#fad0795aa6a6cdaff9ed1b68e9dff94372c232f4"
  integrity sha1-+tB5Wqamza/57Rto6d/5Q3LCMvQ=
  dependencies:
    ini "^1.3.4"
    proto-list "~1.2.1"

convert-source-map@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/convert-source-map/download/convert-source-map-2.0.0.tgz#4b560f649fc4e918dd0ab75cf4961e8bc882d82a"
  integrity sha1-S1YPZJ/E6RjdCrdc9JYei8iC2Co=

copy-anything@^3.0.2:
  version "3.0.5"
  resolved "http://r.npm.sankuai.com/copy-anything/download/copy-anything-3.0.5.tgz#2d92dce8c498f790fa7ad16b01a1ae5a45b020a0"
  integrity sha1-LZLc6MSY95D6etFrAaGuWkWwIKA=
  dependencies:
    is-what "^4.1.8"

cross-spawn@^7.0.6:
  version "7.0.6"
  resolved "http://r.npm.sankuai.com/cross-spawn/download/cross-spawn-7.0.6.tgz#8a58fe78f00dcd70c370451759dfbfaf03e8ee9f"
  integrity sha1-ilj+ePANzXDDcEUXWd+/rwPo7p8=
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

cssesc@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/cssesc/download/cssesc-3.0.0.tgz#37741919903b868565e1c09ea747445cd18983ee"
  integrity sha1-N3QZGZA7hoVl4cCep0dEXNGJg+4=

cssstyle@^4.2.1:
  version "4.3.1"
  resolved "http://r.npm.sankuai.com/cssstyle/download/cssstyle-4.3.1.tgz#68a3c9f5a70aa97d5a6ebecc9805e511fc022eb8"
  integrity sha1-aKPJ9acKqX1abr7MmAXlEfwCLrg=
  dependencies:
    "@asamuzakjp/css-color" "^3.1.2"
    rrweb-cssom "^0.8.0"

csstype@^3.1.3:
  version "3.1.3"
  resolved "http://r.npm.sankuai.com/csstype/download/csstype-3.1.3.tgz#d80ff294d114fb0e6ac500fbf85b60137d7eff81"
  integrity sha1-2A/ylNEU+w5qxQD7+FtgE31+/4E=

data-urls@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/data-urls/download/data-urls-5.0.0.tgz#2f76906bce1824429ffecb6920f45a0b30f00dde"
  integrity sha1-L3aQa84YJEKf/stpIPRaCzDwDd4=
  dependencies:
    whatwg-mimetype "^4.0.0"
    whatwg-url "^14.0.0"

de-indent@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/de-indent/download/de-indent-1.0.2.tgz#b2038e846dc33baa5796128d0804b455b8c1e21d"
  integrity sha1-sgOOhG3DO6pXlhKNCAS0VbjB4h0=

debug@4, debug@^4.1.0, debug@^4.3.1, debug@^4.3.2, debug@^4.3.4, debug@^4.3.7, debug@^4.4.0:
  version "4.4.1"
  resolved "http://r.npm.sankuai.com/debug/download/debug-4.4.1.tgz#e5a8bc6cbc4c6cd3e64308b0693a3d4fa550189b"
  integrity sha1-5ai8bLxMbNPmQwiwaTo9T6VQGJs=
  dependencies:
    ms "^2.1.3"

decimal.js@^10.5.0:
  version "10.5.0"
  resolved "http://r.npm.sankuai.com/decimal.js/download/decimal.js-10.5.0.tgz#0f371c7cf6c4898ce0afb09836db73cd82010f22"
  integrity sha1-DzccfPbEiYzgr7CYNttzzYIBDyI=

deep-eql@^5.0.1:
  version "5.0.2"
  resolved "http://r.npm.sankuai.com/deep-eql/download/deep-eql-5.0.2.tgz#4b756d8d770a9257300825d52a2c2cff99c3a341"
  integrity sha1-S3VtjXcKklcwCCXVKiws/5nDo0E=

deep-is@^0.1.3:
  version "0.1.4"
  resolved "http://r.npm.sankuai.com/deep-is/download/deep-is-0.1.4.tgz#a6f2dce612fadd2ef1f519b73551f17e85199831"
  integrity sha1-pvLc5hL63S7x9Rm3NVHxfoUZmDE=

default-browser-id@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/default-browser-id/download/default-browser-id-5.0.0.tgz#a1d98bf960c15082d8a3fa69e83150ccccc3af26"
  integrity sha1-odmL+WDBUILYo/pp6DFQzMzDryY=

default-browser@^5.2.1:
  version "5.2.1"
  resolved "http://r.npm.sankuai.com/default-browser/download/default-browser-5.2.1.tgz#7b7ba61204ff3e425b556869ae6d3e9d9f1712cf"
  integrity sha1-e3umEgT/PkJbVWhprm0+nZ8XEs8=
  dependencies:
    bundle-name "^4.1.0"
    default-browser-id "^5.0.0"

define-lazy-prop@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/define-lazy-prop/download/define-lazy-prop-3.0.0.tgz#dbb19adfb746d7fc6d734a06b72f4a00d021255f"
  integrity sha1-27Ga37dG1/xtc0oGty9KANAhJV8=

eastasianwidth@^0.2.0:
  version "0.2.0"
  resolved "http://r.npm.sankuai.com/eastasianwidth/download/eastasianwidth-0.2.0.tgz#696ce2ec0aa0e6ea93a397ffcf24aa7840c827cb"
  integrity sha1-aWzi7Aqg5uqTo5f/zySqeEDIJ8s=

editorconfig@^1.0.4:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/editorconfig/download/editorconfig-1.0.4.tgz#040c9a8e9a6c5288388b87c2db07028aa89f53a3"
  integrity sha1-BAyajppsUog4i4fC2wcCiqifU6M=
  dependencies:
    "@one-ini/wasm" "0.1.1"
    commander "^10.0.0"
    minimatch "9.0.1"
    semver "^7.5.3"

electron-to-chromium@^1.5.149:
  version "1.5.158"
  resolved "http://r.npm.sankuai.com/electron-to-chromium/download/electron-to-chromium-1.5.158.tgz#e5f01fc7fdf810d9d223e30593e0839c306276d4"
  integrity sha1-5fAfx/34ENnSI+MFk+CDnDBidtQ=

emoji-regex@^8.0.0:
  version "8.0.0"
  resolved "http://r.npm.sankuai.com/emoji-regex/download/emoji-regex-8.0.0.tgz#e818fd69ce5ccfcb404594f842963bf53164cc37"
  integrity sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=

emoji-regex@^9.2.2:
  version "9.2.2"
  resolved "http://r.npm.sankuai.com/emoji-regex/download/emoji-regex-9.2.2.tgz#840c8803b0d8047f4ff0cf963176b32d4ef3ed72"
  integrity sha1-hAyIA7DYBH9P8M+WMXazLU7z7XI=

entities@^4.5.0:
  version "4.5.0"
  resolved "http://r.npm.sankuai.com/entities/download/entities-4.5.0.tgz#5d268ea5e7113ec74c4d033b79ea5a35a488fb48"
  integrity sha1-XSaOpecRPsdMTQM7eepaNaSI+0g=

entities@^6.0.0:
  version "6.0.0"
  resolved "http://r.npm.sankuai.com/entities/download/entities-6.0.0.tgz#09c9e29cb79b0a6459a9b9db9efb418ac5bb8e51"
  integrity sha1-CcninLebCmRZqbnbnvtBisW7jlE=

error-stack-parser-es@^0.1.5:
  version "0.1.5"
  resolved "http://r.npm.sankuai.com/error-stack-parser-es/download/error-stack-parser-es-0.1.5.tgz#15b50b67bea4b6ed6596976ee07c7867ae25bb1c"
  integrity sha1-FbULZ76ktu1llpdu4Hx4Z64luxw=

es-module-lexer@^1.7.0:
  version "1.7.0"
  resolved "http://r.npm.sankuai.com/es-module-lexer/download/es-module-lexer-1.7.0.tgz#9159601561880a85f2734560a9099b2c31e5372a"
  integrity sha1-kVlgFWGICoXyc0VgqQmbLDHlNyo=

esbuild@^0.25.0:
  version "0.25.5"
  resolved "http://r.npm.sankuai.com/esbuild/download/esbuild-0.25.5.tgz#71075054993fdfae76c66586f9b9c1f8d7edd430"
  integrity sha1-cQdQVJk/3652xmWG+bnB+Nft1DA=
  optionalDependencies:
    "@esbuild/aix-ppc64" "0.25.5"
    "@esbuild/android-arm" "0.25.5"
    "@esbuild/android-arm64" "0.25.5"
    "@esbuild/android-x64" "0.25.5"
    "@esbuild/darwin-arm64" "0.25.5"
    "@esbuild/darwin-x64" "0.25.5"
    "@esbuild/freebsd-arm64" "0.25.5"
    "@esbuild/freebsd-x64" "0.25.5"
    "@esbuild/linux-arm" "0.25.5"
    "@esbuild/linux-arm64" "0.25.5"
    "@esbuild/linux-ia32" "0.25.5"
    "@esbuild/linux-loong64" "0.25.5"
    "@esbuild/linux-mips64el" "0.25.5"
    "@esbuild/linux-ppc64" "0.25.5"
    "@esbuild/linux-riscv64" "0.25.5"
    "@esbuild/linux-s390x" "0.25.5"
    "@esbuild/linux-x64" "0.25.5"
    "@esbuild/netbsd-arm64" "0.25.5"
    "@esbuild/netbsd-x64" "0.25.5"
    "@esbuild/openbsd-arm64" "0.25.5"
    "@esbuild/openbsd-x64" "0.25.5"
    "@esbuild/sunos-x64" "0.25.5"
    "@esbuild/win32-arm64" "0.25.5"
    "@esbuild/win32-ia32" "0.25.5"
    "@esbuild/win32-x64" "0.25.5"

escalade@^3.2.0:
  version "3.2.0"
  resolved "http://r.npm.sankuai.com/escalade/download/escalade-3.2.0.tgz#011a3f69856ba189dffa7dc8fcce99d2a87903e5"
  integrity sha1-ARo/aYVroYnf+n3I/M6Z0qh5A+U=

escape-string-regexp@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/escape-string-regexp/download/escape-string-regexp-4.0.0.tgz#14ba83a5d373e3d311e5afca29cf5bfad965bf34"
  integrity sha1-FLqDpdNz49MR5a/KKc9b+tllvzQ=

eslint-config-prettier@^10.0.1:
  version "10.1.5"
  resolved "http://r.npm.sankuai.com/eslint-config-prettier/download/eslint-config-prettier-10.1.5.tgz#00c18d7225043b6fbce6a665697377998d453782"
  integrity sha1-AMGNciUEO2+85qZlaXN3mY1FN4I=

eslint-plugin-oxlint@^0.16.0:
  version "0.16.12"
  resolved "http://r.npm.sankuai.com/eslint-plugin-oxlint/download/eslint-plugin-oxlint-0.16.12.tgz#d08ac0a196a597f8641d4e444e7d4fea1994337e"
  integrity sha1-0IrAoZall/hkHU5ETn1P6hmUM34=
  dependencies:
    jsonc-parser "^3.3.1"

eslint-plugin-playwright@^2.2.0:
  version "2.2.0"
  resolved "http://r.npm.sankuai.com/eslint-plugin-playwright/download/eslint-plugin-playwright-2.2.0.tgz#d7eda21e670274fc0c006e11ba5cc2c8417b2a6e"
  integrity sha1-1+2iHmcCdPwMAG4RulzCyEF7Km4=
  dependencies:
    globals "^13.23.0"

eslint-plugin-prettier@^5.2.2:
  version "5.4.0"
  resolved "http://r.npm.sankuai.com/eslint-plugin-prettier/download/eslint-plugin-prettier-5.4.0.tgz#54d4748904e58eaf1ffe26c4bffa4986ca7f952b"
  integrity sha1-VNR0iQTljq8f/ibEv/pJhsp/lSs=
  dependencies:
    prettier-linter-helpers "^1.0.0"
    synckit "^0.11.0"

eslint-plugin-vue@~10.0.0:
  version "10.0.1"
  resolved "http://r.npm.sankuai.com/eslint-plugin-vue/download/eslint-plugin-vue-10.0.1.tgz#5194318eb76f98ccf7b7d28f0c7103842c7f480f"
  integrity sha1-UZQxjrdvmMz3t9KPDHEDhCx/SA8=
  dependencies:
    "@eslint-community/eslint-utils" "^4.4.0"
    natural-compare "^1.4.0"
    nth-check "^2.1.1"
    postcss-selector-parser "^6.0.15"
    semver "^7.6.3"
    xml-name-validator "^4.0.0"

eslint-scope@^8.2.0, eslint-scope@^8.3.0:
  version "8.3.0"
  resolved "http://r.npm.sankuai.com/eslint-scope/download/eslint-scope-8.3.0.tgz#10cd3a918ffdd722f5f3f7b5b83db9b23c87340d"
  integrity sha1-EM06kY/91yL18/e1uD25sjyHNA0=
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^5.2.0"

eslint-visitor-keys@^3.4.3:
  version "3.4.3"
  resolved "http://r.npm.sankuai.com/eslint-visitor-keys/download/eslint-visitor-keys-3.4.3.tgz#0cd72fe8550e3c2eae156a96a4dddcd1c8ac5800"
  integrity sha1-DNcv6FUOPC6uFWqWpN3c0cisWAA=

eslint-visitor-keys@^4.2.0:
  version "4.2.0"
  resolved "http://r.npm.sankuai.com/eslint-visitor-keys/download/eslint-visitor-keys-4.2.0.tgz#687bacb2af884fcdda8a6e7d65c606f46a14cd45"
  integrity sha1-aHussq+IT83aim59ZcYG9GoUzUU=

eslint@^9.22.0:
  version "9.27.0"
  resolved "http://r.npm.sankuai.com/eslint/download/eslint-9.27.0.tgz#a587d3cd5b844b68df7898944323a702afe38979"
  integrity sha1-pYfTzVuES2jfeJiUQyOnAq/jiXk=
  dependencies:
    "@eslint-community/eslint-utils" "^4.2.0"
    "@eslint-community/regexpp" "^4.12.1"
    "@eslint/config-array" "^0.20.0"
    "@eslint/config-helpers" "^0.2.1"
    "@eslint/core" "^0.14.0"
    "@eslint/eslintrc" "^3.3.1"
    "@eslint/js" "9.27.0"
    "@eslint/plugin-kit" "^0.3.1"
    "@humanfs/node" "^0.16.6"
    "@humanwhocodes/module-importer" "^1.0.1"
    "@humanwhocodes/retry" "^0.4.2"
    "@types/estree" "^1.0.6"
    "@types/json-schema" "^7.0.15"
    ajv "^6.12.4"
    chalk "^4.0.0"
    cross-spawn "^7.0.6"
    debug "^4.3.2"
    escape-string-regexp "^4.0.0"
    eslint-scope "^8.3.0"
    eslint-visitor-keys "^4.2.0"
    espree "^10.3.0"
    esquery "^1.5.0"
    esutils "^2.0.2"
    fast-deep-equal "^3.1.3"
    file-entry-cache "^8.0.0"
    find-up "^5.0.0"
    glob-parent "^6.0.2"
    ignore "^5.2.0"
    imurmurhash "^0.1.4"
    is-glob "^4.0.0"
    json-stable-stringify-without-jsonify "^1.0.1"
    lodash.merge "^4.6.2"
    minimatch "^3.1.2"
    natural-compare "^1.4.0"
    optionator "^0.9.3"

espree@^10.0.1, espree@^10.3.0:
  version "10.3.0"
  resolved "http://r.npm.sankuai.com/espree/download/espree-10.3.0.tgz#29267cf5b0cb98735b65e64ba07e0ed49d1eed8a"
  integrity sha1-KSZ89bDLmHNbZeZLoH4O1J0e7Yo=
  dependencies:
    acorn "^8.14.0"
    acorn-jsx "^5.3.2"
    eslint-visitor-keys "^4.2.0"

esquery@^1.5.0, esquery@^1.6.0:
  version "1.6.0"
  resolved "http://r.npm.sankuai.com/esquery/download/esquery-1.6.0.tgz#91419234f804d852a82dceec3e16cdc22cf9dae7"
  integrity sha1-kUGSNPgE2FKoLc7sPhbNwiz52uc=
  dependencies:
    estraverse "^5.1.0"

esrecurse@^4.3.0:
  version "4.3.0"
  resolved "http://r.npm.sankuai.com/esrecurse/download/esrecurse-4.3.0.tgz#7ad7964d679abb28bee72cec63758b1c5d2c9921"
  integrity sha1-eteWTWeauyi+5yzsY3WLHF0smSE=
  dependencies:
    estraverse "^5.2.0"

estraverse@^5.1.0, estraverse@^5.2.0:
  version "5.3.0"
  resolved "http://r.npm.sankuai.com/estraverse/download/estraverse-5.3.0.tgz#2eea5290702f26ab8fe5370370ff86c965d21123"
  integrity sha1-LupSkHAvJquP5TcDcP+GyWXSESM=

estree-walker@^2.0.2:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/estree-walker/download/estree-walker-2.0.2.tgz#52f010178c2a4c117a7757cfe942adb7d2da4cac"
  integrity sha1-UvAQF4wqTBF6d1fP6UKtt9LaTKw=

estree-walker@^3.0.3:
  version "3.0.3"
  resolved "http://r.npm.sankuai.com/estree-walker/download/estree-walker-3.0.3.tgz#67c3e549ec402a487b4fc193d1953a524752340d"
  integrity sha1-Z8PlSexAKkh7T8GT0ZU6UkdSNA0=
  dependencies:
    "@types/estree" "^1.0.0"

esutils@^2.0.2:
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/esutils/download/esutils-2.0.3.tgz#74d2eb4de0b8da1293711910d50775b9b710ef64"
  integrity sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=

execa@^9.5.2:
  version "9.6.0"
  resolved "http://r.npm.sankuai.com/execa/download/execa-9.6.0.tgz#38665530e54e2e018384108322f37f35ae74f3bc"
  integrity sha1-OGZVMOVOLgGDhBCDIvN/Na5087w=
  dependencies:
    "@sindresorhus/merge-streams" "^4.0.0"
    cross-spawn "^7.0.6"
    figures "^6.1.0"
    get-stream "^9.0.0"
    human-signals "^8.0.1"
    is-plain-obj "^4.1.0"
    is-stream "^4.0.1"
    npm-run-path "^6.0.0"
    pretty-ms "^9.2.0"
    signal-exit "^4.1.0"
    strip-final-newline "^4.0.0"
    yoctocolors "^2.1.1"

expect-type@^1.2.1:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/expect-type/download/expect-type-1.2.1.tgz#af76d8b357cf5fa76c41c09dafb79c549e75f71f"
  integrity sha1-r3bYs1fPX6dsQcCdr7ecVJ519x8=

fast-deep-equal@^3.1.1, fast-deep-equal@^3.1.3:
  version "3.1.3"
  resolved "http://r.npm.sankuai.com/fast-deep-equal/download/fast-deep-equal-3.1.3.tgz#3a7d56b559d6cbc3eb512325244e619a65c6c525"
  integrity sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=

fast-diff@^1.1.2:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/fast-diff/download/fast-diff-1.3.0.tgz#ece407fa550a64d638536cd727e129c61616e0f0"
  integrity sha1-7OQH+lUKZNY4U2zXJ+EpxhYW4PA=

fast-glob@^3.3.2, fast-glob@^3.3.3:
  version "3.3.3"
  resolved "http://r.npm.sankuai.com/fast-glob/download/fast-glob-3.3.3.tgz#d06d585ce8dba90a16b0505c543c3ccfb3aeb818"
  integrity sha1-0G1YXOjbqQoWsFBcVDw8z7OuuBg=
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.2"
    merge2 "^1.3.0"
    micromatch "^4.0.8"

fast-json-stable-stringify@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/fast-json-stable-stringify/download/fast-json-stable-stringify-2.1.0.tgz#874bf69c6f404c2b5d99c481341399fd55892633"
  integrity sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=

fast-levenshtein@^2.0.6:
  version "2.0.6"
  resolved "http://r.npm.sankuai.com/fast-levenshtein/download/fast-levenshtein-2.0.6.tgz#3d8a5c66883a16a30ca8643e851f19baa7797917"
  integrity sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=

fastq@^1.6.0:
  version "1.19.1"
  resolved "http://r.npm.sankuai.com/fastq/download/fastq-1.19.1.tgz#d50eaba803c8846a883c16492821ebcd2cda55f5"
  integrity sha1-1Q6rqAPIhGqIPBZJKCHrzSzaVfU=
  dependencies:
    reusify "^1.0.4"

fdir@^6.4.4:
  version "6.4.5"
  resolved "http://r.npm.sankuai.com/fdir/download/fdir-6.4.5.tgz#328e280f3a23699362f95f2e82acf978a0c0cb49"
  integrity sha1-Mo4oDzojaZNi+V8ugqz5eKDAy0k=

figures@^6.1.0:
  version "6.1.0"
  resolved "http://r.npm.sankuai.com/figures/download/figures-6.1.0.tgz#935479f51865fa7479f6fa94fc6fc7ac14e62c4a"
  integrity sha1-k1R59Rhl+nR59vqU/G/HrBTmLEo=
  dependencies:
    is-unicode-supported "^2.0.0"

file-entry-cache@^8.0.0:
  version "8.0.0"
  resolved "http://r.npm.sankuai.com/file-entry-cache/download/file-entry-cache-8.0.0.tgz#7787bddcf1131bffb92636c69457bbc0edd6d81f"
  integrity sha1-d4e93PETG/+5JjbGlFe7wO3W2B8=
  dependencies:
    flat-cache "^4.0.0"

fill-range@^7.1.1:
  version "7.1.1"
  resolved "http://r.npm.sankuai.com/fill-range/download/fill-range-7.1.1.tgz#44265d3cac07e3ea7dc247516380643754a05292"
  integrity sha1-RCZdPKwH4+p9wkdRY4BkN1SgUpI=
  dependencies:
    to-regex-range "^5.0.1"

find-up@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/find-up/download/find-up-5.0.0.tgz#4c92819ecb7083561e4f4a240a86be5198f536fc"
  integrity sha1-TJKBnstwg1YeT0okCoa+UZj1Nvw=
  dependencies:
    locate-path "^6.0.0"
    path-exists "^4.0.0"

flat-cache@^4.0.0:
  version "4.0.1"
  resolved "http://r.npm.sankuai.com/flat-cache/download/flat-cache-4.0.1.tgz#0ece39fcb14ee012f4b0410bd33dd9c1f011127c"
  integrity sha1-Ds45/LFO4BL0sEEL0z3ZwfAREnw=
  dependencies:
    flatted "^3.2.9"
    keyv "^4.5.4"

flatted@^3.2.9:
  version "3.3.3"
  resolved "http://r.npm.sankuai.com/flatted/download/flatted-3.3.3.tgz#67c8fad95454a7c7abebf74bb78ee74a44023358"
  integrity sha1-Z8j62VRUp8er6/dLt47nSkQCM1g=

foreground-child@^3.1.0:
  version "3.3.1"
  resolved "http://r.npm.sankuai.com/foreground-child/download/foreground-child-3.3.1.tgz#32e8e9ed1b68a3497befb9ac2b6adf92a638576f"
  integrity sha1-Mujp7Rtoo0l777msK2rfkqY4V28=
  dependencies:
    cross-spawn "^7.0.6"
    signal-exit "^4.0.1"

fs-extra@^11.2.0:
  version "11.3.0"
  resolved "http://r.npm.sankuai.com/fs-extra/download/fs-extra-11.3.0.tgz#0daced136bbaf65a555a326719af931adc7a314d"
  integrity sha1-DaztE2u69lpVWjJnGa+TGtx6MU0=
  dependencies:
    graceful-fs "^4.2.0"
    jsonfile "^6.0.1"
    universalify "^2.0.0"

fsevents@2.3.2:
  version "2.3.2"
  resolved "http://r.npm.sankuai.com/fsevents/download/fsevents-2.3.2.tgz#8a526f78b8fdf4623b709e0b975c52c24c02fd1a"
  integrity sha1-ilJveLj99GI7cJ4Ll1xSwkwC/Ro=

fsevents@~2.3.2, fsevents@~2.3.3:
  version "2.3.3"
  resolved "http://r.npm.sankuai.com/fsevents/download/fsevents-2.3.3.tgz#cac6407785d03675a2a5e1a5305c697b347d90d6"
  integrity sha1-ysZAd4XQNnWipeGlMFxpezR9kNY=

gensync@^1.0.0-beta.2:
  version "1.0.0-beta.2"
  resolved "http://r.npm.sankuai.com/gensync/download/gensync-1.0.0-beta.2.tgz#32a6ee76c3d7f52d46b2b1ae5d93fea8580a25e0"
  integrity sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA=

get-stream@^9.0.0:
  version "9.0.1"
  resolved "http://r.npm.sankuai.com/get-stream/download/get-stream-9.0.1.tgz#95157d21df8eb90d1647102b63039b1df60ebd27"
  integrity sha1-lRV9Id+OuQ0WRxArYwObHfYOvSc=
  dependencies:
    "@sec-ant/readable-stream" "^0.4.1"
    is-stream "^4.0.1"

glob-parent@^5.1.2:
  version "5.1.2"
  resolved "http://r.npm.sankuai.com/glob-parent/download/glob-parent-5.1.2.tgz#869832c58034fe68a4093c17dc15e8340d8401c4"
  integrity sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=
  dependencies:
    is-glob "^4.0.1"

glob-parent@^6.0.2:
  version "6.0.2"
  resolved "http://r.npm.sankuai.com/glob-parent/download/glob-parent-6.0.2.tgz#6d237d99083950c79290f24c7642a3de9a28f9e3"
  integrity sha1-bSN9mQg5UMeSkPJMdkKj3poo+eM=
  dependencies:
    is-glob "^4.0.3"

glob@^10.4.2:
  version "10.4.5"
  resolved "http://r.npm.sankuai.com/glob/download/glob-10.4.5.tgz#f4d9f0b90ffdbab09c9d77f5f29b4262517b0956"
  integrity sha1-9NnwuQ/9urCcnXf18ptCYlF7CVY=
  dependencies:
    foreground-child "^3.1.0"
    jackspeak "^3.1.2"
    minimatch "^9.0.4"
    minipass "^7.1.2"
    package-json-from-dist "^1.0.0"
    path-scurry "^1.11.1"

globals@^11.1.0:
  version "11.12.0"
  resolved "http://r.npm.sankuai.com/globals/download/globals-11.12.0.tgz#ab8795338868a0babd8525758018c2a7eb95c42e"
  integrity sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4=

globals@^13.23.0:
  version "13.24.0"
  resolved "http://r.npm.sankuai.com/globals/download/globals-13.24.0.tgz#8432a19d78ce0c1e833949c36adb345400bb1171"
  integrity sha1-hDKhnXjODB6DOUnDats0VAC7EXE=
  dependencies:
    type-fest "^0.20.2"

globals@^14.0.0:
  version "14.0.0"
  resolved "http://r.npm.sankuai.com/globals/download/globals-14.0.0.tgz#898d7413c29babcf6bafe56fcadded858ada724e"
  integrity sha1-iY10E8Kbq89rr+Vvyt3thYrack4=

graceful-fs@^4.1.6, graceful-fs@^4.2.0:
  version "4.2.11"
  resolved "http://r.npm.sankuai.com/graceful-fs/download/graceful-fs-4.2.11.tgz#4183e4e8bf08bb6e05bbb2f7d2e0c8f712ca40e3"
  integrity sha1-QYPk6L8Iu24Fu7L30uDI9xLKQOM=

graphemer@^1.4.0:
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/graphemer/download/graphemer-1.4.0.tgz#fb2f1d55e0e3a1849aeffc90c4fa0dd53a0e66c6"
  integrity sha1-+y8dVeDjoYSa7/yQxPoN1ToOZsY=

has-flag@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/has-flag/download/has-flag-4.0.0.tgz#944771fd9c81c81265c4d6941860da06bb59479b"
  integrity sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=

he@^1.2.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/he/download/he-1.2.0.tgz#84ae65fa7eafb165fddb61566ae14baf05664f0f"
  integrity sha1-hK5l+n6vsWX922FWauFLrwVmTw8=

hookable@^5.5.3:
  version "5.5.3"
  resolved "http://r.npm.sankuai.com/hookable/download/hookable-5.5.3.tgz#6cfc358984a1ef991e2518cb9ed4a778bbd3215d"
  integrity sha1-bPw1iYSh75keJRjLntSneLvTIV0=

html-encoding-sniffer@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/html-encoding-sniffer/download/html-encoding-sniffer-4.0.0.tgz#696df529a7cfd82446369dc5193e590a3735b448"
  integrity sha1-aW31KafP2CRGNp3FGT5ZCjc1tEg=
  dependencies:
    whatwg-encoding "^3.1.1"

http-proxy-agent@^7.0.2:
  version "7.0.2"
  resolved "http://r.npm.sankuai.com/http-proxy-agent/download/http-proxy-agent-7.0.2.tgz#9a8b1f246866c028509486585f62b8f2c18c270e"
  integrity sha1-mosfJGhmwChQlIZYX2K48sGMJw4=
  dependencies:
    agent-base "^7.1.0"
    debug "^4.3.4"

https-proxy-agent@^7.0.6:
  version "7.0.6"
  resolved "http://r.npm.sankuai.com/https-proxy-agent/download/https-proxy-agent-7.0.6.tgz#da8dfeac7da130b05c2ba4b59c9b6cd66611a6b9"
  integrity sha1-2o3+rH2hMLBcK6S1nJts1mYRprk=
  dependencies:
    agent-base "^7.1.2"
    debug "4"

human-signals@^8.0.1:
  version "8.0.1"
  resolved "http://r.npm.sankuai.com/human-signals/download/human-signals-8.0.1.tgz#f08bb593b6d1db353933d06156cedec90abe51fb"
  integrity sha1-8Iu1k7bR2zU5M9BhVs7eyQq+Ufs=

iconv-lite@0.6.3:
  version "0.6.3"
  resolved "http://r.npm.sankuai.com/iconv-lite/download/iconv-lite-0.6.3.tgz#a52f80bf38da1952eb5c681790719871a1a72501"
  integrity sha1-pS+AvzjaGVLrXGgXkHGYcaGnJQE=
  dependencies:
    safer-buffer ">= 2.1.2 < 3.0.0"

ignore@^5.2.0:
  version "5.3.2"
  resolved "http://r.npm.sankuai.com/ignore/download/ignore-5.3.2.tgz#3cd40e729f3643fd87cb04e50bf0eb722bc596f5"
  integrity sha1-PNQOcp82Q/2HywTlC/DrcivFlvU=

ignore@^7.0.0:
  version "7.0.4"
  resolved "http://r.npm.sankuai.com/ignore/download/ignore-7.0.4.tgz#a12c70d0f2607c5bf508fb65a40c75f037d7a078"
  integrity sha1-oSxw0PJgfFv1CPtlpAx18DfXoHg=

import-fresh@^3.2.1:
  version "3.3.1"
  resolved "http://r.npm.sankuai.com/import-fresh/download/import-fresh-3.3.1.tgz#9cecb56503c0ada1f2741dbbd6546e4b13b57ccf"
  integrity sha1-nOy1ZQPAraHydB271lRuSxO1fM8=
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "http://r.npm.sankuai.com/imurmurhash/download/imurmurhash-0.1.4.tgz#9218b9b2b928a238b13dc4fb6b6d576f231453ea"
  integrity sha1-khi5srkoojixPcT7a21XbyMUU+o=

ini@^1.3.4:
  version "1.3.8"
  resolved "http://r.npm.sankuai.com/ini/download/ini-1.3.8.tgz#a29da425b48806f34767a4efce397269af28432c"
  integrity sha1-op2kJbSIBvNHZ6Tvzjlyaa8oQyw=

is-docker@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/is-docker/download/is-docker-3.0.0.tgz#90093aa3106277d8a77a5910dbae71747e15a200"
  integrity sha1-kAk6oxBid9inelkQ265xdH4VogA=

is-extglob@^2.1.1:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/is-extglob/download/is-extglob-2.1.1.tgz#a88c02535791f02ed37c76a1b9ea9773c833f8c2"
  integrity sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz#f116f8064fe90b3f7844a38997c0b75051269f1d"
  integrity sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=

is-glob@^4.0.0, is-glob@^4.0.1, is-glob@^4.0.3:
  version "4.0.3"
  resolved "http://r.npm.sankuai.com/is-glob/download/is-glob-4.0.3.tgz#64f61e42cbbb2eec2071a9dac0b28ba1e65d5084"
  integrity sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=
  dependencies:
    is-extglob "^2.1.1"

is-inside-container@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/is-inside-container/download/is-inside-container-1.0.0.tgz#e81fba699662eb31dbdaf26766a61d4814717ea4"
  integrity sha1-6B+6aZZi6zHb2vJnZqYdSBRxfqQ=
  dependencies:
    is-docker "^3.0.0"

is-number@^7.0.0:
  version "7.0.0"
  resolved "http://r.npm.sankuai.com/is-number/download/is-number-7.0.0.tgz#7535345b896734d5f80c4d06c50955527a14f12b"
  integrity sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=

is-plain-obj@^4.1.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/is-plain-obj/download/is-plain-obj-4.1.0.tgz#d65025edec3657ce032fd7db63c97883eaed71f0"
  integrity sha1-1lAl7ew2V84DL9fbY8l4g+rtcfA=

is-potential-custom-element-name@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/is-potential-custom-element-name/download/is-potential-custom-element-name-1.0.1.tgz#171ed6f19e3ac554394edf78caa05784a45bebb5"
  integrity sha1-Fx7W8Z46xVQ5Tt94yqBXhKRb67U=

is-stream@^4.0.1:
  version "4.0.1"
  resolved "http://r.npm.sankuai.com/is-stream/download/is-stream-4.0.1.tgz#375cf891e16d2e4baec250b85926cffc14720d9b"
  integrity sha1-N1z4keFtLkuuwlC4WSbP/BRyDZs=

is-unicode-supported@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/is-unicode-supported/download/is-unicode-supported-2.1.0.tgz#09f0ab0de6d3744d48d265ebb98f65d11f2a9b3a"
  integrity sha1-CfCrDebTdE1I0mXruY9l0R8qmzo=

is-what@^4.1.8:
  version "4.1.16"
  resolved "http://r.npm.sankuai.com/is-what/download/is-what-4.1.16.tgz#1ad860a19da8b4895ad5495da3182ce2acdd7a6f"
  integrity sha1-GthgoZ2otIla1Uldoxgs4qzdem8=

is-wsl@^3.1.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/is-wsl/download/is-wsl-3.1.0.tgz#e1c657e39c10090afcbedec61720f6b924c3cbd2"
  integrity sha1-4cZX45wQCQr8vt7GFyD2uSTDy9I=
  dependencies:
    is-inside-container "^1.0.0"

isexe@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/isexe/download/isexe-2.0.0.tgz#e8fbf374dc556ff8947a10dcb0572d633f2cfa10"
  integrity sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=

isexe@^3.1.1:
  version "3.1.1"
  resolved "http://r.npm.sankuai.com/isexe/download/isexe-3.1.1.tgz#4a407e2bd78ddfb14bea0c27c6f7072dde775f0d"
  integrity sha1-SkB+K9eN37FL6gwnxvcHLd53Xw0=

jackspeak@^3.1.2:
  version "3.4.3"
  resolved "http://r.npm.sankuai.com/jackspeak/download/jackspeak-3.4.3.tgz#8833a9d89ab4acde6188942bd1c53b6390ed5a8a"
  integrity sha1-iDOp2Jq0rN5hiJQr0cU7Y5DtWoo=
  dependencies:
    "@isaacs/cliui" "^8.0.2"
  optionalDependencies:
    "@pkgjs/parseargs" "^0.11.0"

jiti@^2.4.2:
  version "2.4.2"
  resolved "http://r.npm.sankuai.com/jiti/download/jiti-2.4.2.tgz#d19b7732ebb6116b06e2038da74a55366faef560"
  integrity sha1-0Zt3Muu2EWsG4gONp0pVNm+u9WA=

js-beautify@^1.14.9:
  version "1.15.4"
  resolved "http://r.npm.sankuai.com/js-beautify/download/js-beautify-1.15.4.tgz#f579f977ed4c930cef73af8f98f3f0a608acd51e"
  integrity sha1-9Xn5d+1Mkwzvc6+PmPPwpgis1R4=
  dependencies:
    config-chain "^1.1.13"
    editorconfig "^1.0.4"
    glob "^10.4.2"
    js-cookie "^3.0.5"
    nopt "^7.2.1"

js-cookie@^3.0.5:
  version "3.0.5"
  resolved "http://r.npm.sankuai.com/js-cookie/download/js-cookie-3.0.5.tgz#0b7e2fd0c01552c58ba86e0841f94dc2557dcdbc"
  integrity sha1-C34v0MAVUsWLqG4IQflNwlV9zbw=

js-tokens@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/js-tokens/download/js-tokens-4.0.0.tgz#19203fb59991df98e3a287050d4647cdeaf32499"
  integrity sha1-GSA/tZmR35jjoocFDUZHzerzJJk=

js-yaml@^4.1.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/js-yaml/download/js-yaml-4.1.0.tgz#c1fb65f8f5017901cdd2c951864ba18458a10602"
  integrity sha1-wftl+PUBeQHN0slRhkuhhFihBgI=
  dependencies:
    argparse "^2.0.1"

jsdom@^26.0.0:
  version "26.1.0"
  resolved "http://r.npm.sankuai.com/jsdom/download/jsdom-26.1.0.tgz#ab5f1c1cafc04bd878725490974ea5e8bf0c72b3"
  integrity sha1-q18cHK/AS9h4clSQl06l6L8McrM=
  dependencies:
    cssstyle "^4.2.1"
    data-urls "^5.0.0"
    decimal.js "^10.5.0"
    html-encoding-sniffer "^4.0.0"
    http-proxy-agent "^7.0.2"
    https-proxy-agent "^7.0.6"
    is-potential-custom-element-name "^1.0.1"
    nwsapi "^2.2.16"
    parse5 "^7.2.1"
    rrweb-cssom "^0.8.0"
    saxes "^6.0.0"
    symbol-tree "^3.2.4"
    tough-cookie "^5.1.1"
    w3c-xmlserializer "^5.0.0"
    webidl-conversions "^7.0.0"
    whatwg-encoding "^3.1.1"
    whatwg-mimetype "^4.0.0"
    whatwg-url "^14.1.1"
    ws "^8.18.0"
    xml-name-validator "^5.0.0"

jsesc@^3.0.2:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/jsesc/download/jsesc-3.1.0.tgz#74d335a234f67ed19907fdadfac7ccf9d409825d"
  integrity sha1-dNM1ojT2ftGZB/2t+sfM+dQJgl0=

json-buffer@3.0.1:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/json-buffer/download/json-buffer-3.0.1.tgz#9338802a30d3b6605fbe0613e094008ca8c05a13"
  integrity sha1-kziAKjDTtmBfvgYT4JQAjKjAWhM=

json-parse-even-better-errors@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/json-parse-even-better-errors/download/json-parse-even-better-errors-4.0.0.tgz#d3f67bd5925e81d3e31aa466acc821c8375cec43"
  integrity sha1-0/Z71ZJegdPjGqRmrMghyDdc7EM=

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "http://r.npm.sankuai.com/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz#69f6a87d9513ab8bb8fe63bdb0979c448e684660"
  integrity sha1-afaofZUTq4u4/mO9sJecRI5oRmA=

json-stable-stringify-without-jsonify@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/json-stable-stringify-without-jsonify/download/json-stable-stringify-without-jsonify-1.0.1.tgz#9db7b59496ad3f3cfef30a75142d2d930ad72651"
  integrity sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=

json5@^2.2.3:
  version "2.2.3"
  resolved "http://r.npm.sankuai.com/json5/download/json5-2.2.3.tgz#78cd6f1a19bdc12b73db5ad0c61efd66c1e29283"
  integrity sha1-eM1vGhm9wStz21rQxh79ZsHikoM=

jsonc-parser@^3.3.1:
  version "3.3.1"
  resolved "http://r.npm.sankuai.com/jsonc-parser/download/jsonc-parser-3.3.1.tgz#f2a524b4f7fd11e3d791e559977ad60b98b798b4"
  integrity sha1-8qUktPf9EePXkeVZl3rWC5i3mLQ=

jsonfile@^6.0.1:
  version "6.1.0"
  resolved "http://r.npm.sankuai.com/jsonfile/download/jsonfile-6.1.0.tgz#bc55b2634793c679ec6403094eb13698a6ec0aae"
  integrity sha1-vFWyY0eTxnnsZAMJTrE2mKbsCq4=
  dependencies:
    universalify "^2.0.0"
  optionalDependencies:
    graceful-fs "^4.1.6"

keyv@^4.5.4:
  version "4.5.4"
  resolved "http://r.npm.sankuai.com/keyv/download/keyv-4.5.4.tgz#a879a99e29452f942439f2a405e3af8b31d4de93"
  integrity sha1-qHmpnilFL5QkOfKkBeOvizHU3pM=
  dependencies:
    json-buffer "3.0.1"

kolorist@^1.8.0:
  version "1.8.0"
  resolved "http://r.npm.sankuai.com/kolorist/download/kolorist-1.8.0.tgz#edddbbbc7894bc13302cdf740af6374d4a04743c"
  integrity sha1-7d27vHiUvBMwLN90CvY3TUoEdDw=

levn@^0.4.1:
  version "0.4.1"
  resolved "http://r.npm.sankuai.com/levn/download/levn-0.4.1.tgz#ae4562c007473b932a6200d403268dd2fffc6ade"
  integrity sha1-rkViwAdHO5MqYgDUAyaN0v/8at4=
  dependencies:
    prelude-ls "^1.2.1"
    type-check "~0.4.0"

locate-path@^6.0.0:
  version "6.0.0"
  resolved "http://r.npm.sankuai.com/locate-path/download/locate-path-6.0.0.tgz#55321eb309febbc59c4801d931a72452a681d286"
  integrity sha1-VTIeswn+u8WcSAHZMackUqaB0oY=
  dependencies:
    p-locate "^5.0.0"

lodash.merge@^4.6.2:
  version "4.6.2"
  resolved "http://r.npm.sankuai.com/lodash.merge/download/lodash.merge-4.6.2.tgz#558aa53b43b661e1925a0afdfa36a9a1085fe57a"
  integrity sha1-VYqlO0O2YeGSWgr9+japoQhf5Xo=

lodash@^4.17.21:
  version "4.17.21"
  resolved "http://r.npm.sankuai.com/lodash/download/lodash-4.17.21.tgz#679591c564c3bffaae8454cf0b3df370c3d6911c"
  integrity sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw=

loupe@^3.1.0, loupe@^3.1.3:
  version "3.1.3"
  resolved "http://r.npm.sankuai.com/loupe/download/loupe-3.1.3.tgz#042a8f7986d77f3d0f98ef7990a2b2fef18b0fd2"
  integrity sha1-BCqPeYbXfz0PmO95kKKy/vGLD9I=

lru-cache@^10.2.0, lru-cache@^10.4.3:
  version "10.4.3"
  resolved "http://r.npm.sankuai.com/lru-cache/download/lru-cache-10.4.3.tgz#410fc8a17b70e598013df257c2446b7f3383f119"
  integrity sha1-QQ/IoXtw5ZgBPfJXwkRrfzOD8Rk=

lru-cache@^5.1.1:
  version "5.1.1"
  resolved "http://r.npm.sankuai.com/lru-cache/download/lru-cache-5.1.1.tgz#1da27e6710271947695daf6848e847f01d84b920"
  integrity sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=
  dependencies:
    yallist "^3.0.2"

magic-string@^0.30.17, magic-string@^0.30.4:
  version "0.30.17"
  resolved "http://r.npm.sankuai.com/magic-string/download/magic-string-0.30.17.tgz#450a449673d2460e5bbcfba9a61916a1714c7453"
  integrity sha1-RQpElnPSRg5bvPupphkWoXFMdFM=
  dependencies:
    "@jridgewell/sourcemap-codec" "^1.5.0"

memorystream@^0.3.1:
  version "0.3.1"
  resolved "http://r.npm.sankuai.com/memorystream/download/memorystream-0.3.1.tgz#86d7090b30ce455d63fbae12dda51a47ddcaf9b2"
  integrity sha1-htcJCzDORV1j+64S3aUaR93K+bI=

merge2@^1.3.0:
  version "1.4.1"
  resolved "http://r.npm.sankuai.com/merge2/download/merge2-1.4.1.tgz#4368892f885e907455a6fd7dc55c0c9d404990ae"
  integrity sha1-Q2iJL4hekHRVpv19xVwMnUBJkK4=

micromatch@^4.0.8:
  version "4.0.8"
  resolved "http://r.npm.sankuai.com/micromatch/download/micromatch-4.0.8.tgz#d66fa18f3a47076789320b9b1af32bd86d9fa202"
  integrity sha1-1m+hjzpHB2eJMgubGvMr2G2fogI=
  dependencies:
    braces "^3.0.3"
    picomatch "^2.3.1"

minimatch@9.0.1:
  version "9.0.1"
  resolved "http://r.npm.sankuai.com/minimatch/download/minimatch-9.0.1.tgz#8a555f541cf976c622daf078bb28f29fb927c253"
  integrity sha1-ilVfVBz5dsYi2vB4uyjyn7knwlM=
  dependencies:
    brace-expansion "^2.0.1"

minimatch@^3.1.2:
  version "3.1.2"
  resolved "http://r.npm.sankuai.com/minimatch/download/minimatch-3.1.2.tgz#19cd194bfd3e428f049a70817c038d89ab4be35b"
  integrity sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=
  dependencies:
    brace-expansion "^1.1.7"

minimatch@^9.0.0, minimatch@^9.0.3, minimatch@^9.0.4:
  version "9.0.5"
  resolved "http://r.npm.sankuai.com/minimatch/download/minimatch-9.0.5.tgz#d74f9dd6b57d83d8e98cfb82133b03978bc929e5"
  integrity sha1-10+d1rV9g9jpjPuCEzsDl4vJKeU=
  dependencies:
    brace-expansion "^2.0.1"

"minipass@^5.0.0 || ^6.0.2 || ^7.0.0", minipass@^7.1.2:
  version "7.1.2"
  resolved "http://r.npm.sankuai.com/minipass/download/minipass-7.1.2.tgz#93a9626ce5e5e66bd4db86849e7515e92340a707"
  integrity sha1-k6libOXl5mvU24aEnnUV6SNApwc=

mitt@^3.0.1:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/mitt/download/mitt-3.0.1.tgz#ea36cf0cc30403601ae074c8f77b7092cdab36d1"
  integrity sha1-6jbPDMMEA2Aa4HTI93twks2rNtE=

mrmime@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/mrmime/download/mrmime-2.0.1.tgz#bc3e87f7987853a54c9850eeb1f1078cd44adddc"
  integrity sha1-vD6H95h4U6VMmFDusfEHjNRK3dw=

ms@^2.1.3:
  version "2.1.3"
  resolved "http://r.npm.sankuai.com/ms/download/ms-2.1.3.tgz#574c8138ce1d2b5861f0b44579dbadd60c6615b2"
  integrity sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=

muggle-string@^0.4.1:
  version "0.4.1"
  resolved "http://r.npm.sankuai.com/muggle-string/download/muggle-string-0.4.1.tgz#3b366bd43b32f809dc20659534dd30e7c8a0d328"
  integrity sha1-OzZr1Dsy+AncIGWVNN0w58ig0yg=

nanoid@^3.3.8:
  version "3.3.11"
  resolved "http://r.npm.sankuai.com/nanoid/download/nanoid-3.3.11.tgz#4f4f112cefbe303202f2199838128936266d185b"
  integrity sha1-T08RLO++MDIC8hmYOBKJNiZtGFs=

nanoid@^5.1.0:
  version "5.1.5"
  resolved "http://r.npm.sankuai.com/nanoid/download/nanoid-5.1.5.tgz#f7597f9d9054eb4da9548cdd53ca70f1790e87de"
  integrity sha1-91l/nZBU602pVIzdU8pw8XkOh94=

natural-compare@^1.4.0:
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/natural-compare/download/natural-compare-1.4.0.tgz#4abebfeed7541f2c27acfb29bdbbd15c8d5ba4f7"
  integrity sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=

node-releases@^2.0.19:
  version "2.0.19"
  resolved "http://r.npm.sankuai.com/node-releases/download/node-releases-2.0.19.tgz#9e445a52950951ec4d177d843af370b411caf314"
  integrity sha1-nkRaUpUJUexNF32EOvNwtBHK8xQ=

nopt@^7.2.1:
  version "7.2.1"
  resolved "http://r.npm.sankuai.com/nopt/download/nopt-7.2.1.tgz#1cac0eab9b8e97c9093338446eddd40b2c8ca1e7"
  integrity sha1-HKwOq5uOl8kJMzhEbt3UCyyMoec=
  dependencies:
    abbrev "^2.0.0"

npm-normalize-package-bin@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/npm-normalize-package-bin/download/npm-normalize-package-bin-4.0.0.tgz#df79e70cd0a113b77c02d1fe243c96b8e618acb1"
  integrity sha1-33nnDNChE7d8AtH+JDyWuOYYrLE=

npm-run-all2@^7.0.2:
  version "7.0.2"
  resolved "http://r.npm.sankuai.com/npm-run-all2/download/npm-run-all2-7.0.2.tgz#26155c140b5e3f1155efd7f5d67212c8027b397c"
  integrity sha1-JhVcFAtePxFV79f11nISyAJ7OXw=
  dependencies:
    ansi-styles "^6.2.1"
    cross-spawn "^7.0.6"
    memorystream "^0.3.1"
    minimatch "^9.0.0"
    pidtree "^0.6.0"
    read-package-json-fast "^4.0.0"
    shell-quote "^1.7.3"
    which "^5.0.0"

npm-run-path@^6.0.0:
  version "6.0.0"
  resolved "http://r.npm.sankuai.com/npm-run-path/download/npm-run-path-6.0.0.tgz#25cfdc4eae04976f3349c0b1afc089052c362537"
  integrity sha1-Jc/cTq4El28zScCxr8CJBSw2JTc=
  dependencies:
    path-key "^4.0.0"
    unicorn-magic "^0.3.0"

nth-check@^2.1.1:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/nth-check/download/nth-check-2.1.1.tgz#c9eab428effce36cd6b92c924bdb000ef1f1ed1d"
  integrity sha1-yeq0KO/842zWuSySS9sADvHx7R0=
  dependencies:
    boolbase "^1.0.0"

nwsapi@^2.2.16:
  version "2.2.20"
  resolved "http://r.npm.sankuai.com/nwsapi/download/nwsapi-2.2.20.tgz#22e53253c61e7b0e7e93cef42c891154bcca11ef"
  integrity sha1-IuUyU8Yeew5+k870LIkRVLzKEe8=

open@^10.1.0:
  version "10.1.2"
  resolved "http://r.npm.sankuai.com/open/download/open-10.1.2.tgz#d5df40984755c9a9c3c93df8156a12467e882925"
  integrity sha1-1d9AmEdVyanDyT34FWoSRn6IKSU=
  dependencies:
    default-browser "^5.2.1"
    define-lazy-prop "^3.0.0"
    is-inside-container "^1.0.0"
    is-wsl "^3.1.0"

optionator@^0.9.3:
  version "0.9.4"
  resolved "http://r.npm.sankuai.com/optionator/download/optionator-0.9.4.tgz#7ea1c1a5d91d764fb282139c88fe11e182a3a734"
  integrity sha1-fqHBpdkddk+yghOciP4R4YKjpzQ=
  dependencies:
    deep-is "^0.1.3"
    fast-levenshtein "^2.0.6"
    levn "^0.4.1"
    prelude-ls "^1.2.1"
    type-check "^0.4.0"
    word-wrap "^1.2.5"

oxlint@^0.16.0:
  version "0.16.12"
  resolved "http://r.npm.sankuai.com/oxlint/download/oxlint-0.16.12.tgz#0b0b22abe0a011212b524175d2f302d15a657ff8"
  integrity sha1-Cwsiq+CgESErUkF10vMC0Vplf/g=
  optionalDependencies:
    "@oxlint/darwin-arm64" "0.16.12"
    "@oxlint/darwin-x64" "0.16.12"
    "@oxlint/linux-arm64-gnu" "0.16.12"
    "@oxlint/linux-arm64-musl" "0.16.12"
    "@oxlint/linux-x64-gnu" "0.16.12"
    "@oxlint/linux-x64-musl" "0.16.12"
    "@oxlint/win32-arm64" "0.16.12"
    "@oxlint/win32-x64" "0.16.12"

p-limit@^3.0.2:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/p-limit/download/p-limit-3.1.0.tgz#e1daccbe78d0d1388ca18c64fea38e3e57e3706b"
  integrity sha1-4drMvnjQ0TiMoYxk/qOOPlfjcGs=
  dependencies:
    yocto-queue "^0.1.0"

p-locate@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/p-locate/download/p-locate-5.0.0.tgz#83c8315c6785005e3bd021839411c9e110e6d834"
  integrity sha1-g8gxXGeFAF470CGDlBHJ4RDm2DQ=
  dependencies:
    p-limit "^3.0.2"

package-json-from-dist@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/package-json-from-dist/download/package-json-from-dist-1.0.1.tgz#4f1471a010827a86f94cfd9b0727e36d267de505"
  integrity sha1-TxRxoBCCeob5TP2bByfjbSZ95QU=

parent-module@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/parent-module/download/parent-module-1.0.1.tgz#691d2709e78c79fae3a156622452d00762caaaa2"
  integrity sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=
  dependencies:
    callsites "^3.0.0"

parse-ms@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/parse-ms/download/parse-ms-4.0.0.tgz#c0c058edd47c2a590151a718990533fd62803df4"
  integrity sha1-wMBY7dR8KlkBUacYmQUz/WKAPfQ=

parse5@^7.0.0, parse5@^7.2.1:
  version "7.3.0"
  resolved "http://r.npm.sankuai.com/parse5/download/parse5-7.3.0.tgz#d7e224fa72399c7a175099f45fc2ad024b05ec05"
  integrity sha1-1+Ik+nI5nHoXUJn0X8KtAksF7AU=
  dependencies:
    entities "^6.0.0"

path-browserify@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/path-browserify/download/path-browserify-1.0.1.tgz#d98454a9c3753d5790860f16f68867b9e46be1fd"
  integrity sha1-2YRUqcN1PVeQhg8W9ohnueRr4f0=

path-exists@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/path-exists/download/path-exists-4.0.0.tgz#513bdbe2d3b95d7762e8c1137efa195c6c61b5b3"
  integrity sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=

path-key@^3.1.0:
  version "3.1.1"
  resolved "http://r.npm.sankuai.com/path-key/download/path-key-3.1.1.tgz#581f6ade658cbba65a0d3380de7753295054f375"
  integrity sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=

path-key@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/path-key/download/path-key-4.0.0.tgz#295588dc3aee64154f877adb9d780b81c554bf18"
  integrity sha1-KVWI3DruZBVPh3rbnXgLgcVUvxg=

path-scurry@^1.11.1:
  version "1.11.1"
  resolved "http://r.npm.sankuai.com/path-scurry/download/path-scurry-1.11.1.tgz#7960a668888594a0720b12a911d1a742ab9f11d2"
  integrity sha1-eWCmaIiFlKByCxKpEdGnQqufEdI=
  dependencies:
    lru-cache "^10.2.0"
    minipass "^5.0.0 || ^6.0.2 || ^7.0.0"

pathe@^2.0.3:
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/pathe/download/pathe-2.0.3.tgz#3ecbec55421685b70a9da872b2cff3e1cbed1716"
  integrity sha1-PsvsVUIWhbcKnahyss/z4cvtFxY=

pathval@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/pathval/download/pathval-2.0.0.tgz#7e2550b422601d4f6b8e26f1301bc8f15a741a25"
  integrity sha1-fiVQtCJgHU9rjibxMBvI8Vp0GiU=

perfect-debounce@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/perfect-debounce/download/perfect-debounce-1.0.0.tgz#9c2e8bc30b169cc984a58b7d5b28049839591d2a"
  integrity sha1-nC6LwwsWnMmEpYt9WygEmDlZHSo=

picocolors@^1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/picocolors/download/picocolors-1.1.1.tgz#3d321af3eab939b083c8f929a1d12cda81c26b6b"
  integrity sha1-PTIa8+q5ObCDyPkpodEs2oHCa2s=

picomatch@^2.3.1:
  version "2.3.1"
  resolved "http://r.npm.sankuai.com/picomatch/download/picomatch-2.3.1.tgz#3ba3833733646d9d3e4995946c1365a67fb07a42"
  integrity sha1-O6ODNzNkbZ0+SZWUbBNlpn+wekI=

picomatch@^4.0.2:
  version "4.0.2"
  resolved "http://r.npm.sankuai.com/picomatch/download/picomatch-4.0.2.tgz#77c742931e8f3b8820946c76cd0c1f13730d1dab"
  integrity sha1-d8dCkx6PO4gglGx2zQwfE3MNHas=

pidtree@^0.6.0:
  version "0.6.0"
  resolved "http://r.npm.sankuai.com/pidtree/download/pidtree-0.6.0.tgz#90ad7b6d42d5841e69e0a2419ef38f8883aa057c"
  integrity sha1-kK17bULVhB5p4KJBnvOPiIOqBXw=

pinia@^3.0.1:
  version "3.0.2"
  resolved "http://r.npm.sankuai.com/pinia/download/pinia-3.0.2.tgz#0616c2e1b39915f253c7626db3c81b7cdad695da"
  integrity sha1-BhbC4bOZFfJTx2Jts8gbfNrWldo=
  dependencies:
    "@vue/devtools-api" "^7.7.2"

playwright-core@1.52.0:
  version "1.52.0"
  resolved "http://r.npm.sankuai.com/playwright-core/download/playwright-core-1.52.0.tgz#238f1f0c3edd4ebba0434ce3f4401900319a3dca"
  integrity sha1-I48fDD7dTrugQ0zj9EAZADGaPco=

playwright@1.52.0:
  version "1.52.0"
  resolved "http://r.npm.sankuai.com/playwright/download/playwright-1.52.0.tgz#26cb9a63346651e1c54c8805acfd85683173d4bd"
  integrity sha1-JsuaYzRmUeHFTIgFrP2FaDFz1L0=
  dependencies:
    playwright-core "1.52.0"
  optionalDependencies:
    fsevents "2.3.2"

postcss-selector-parser@^6.0.15:
  version "6.1.2"
  resolved "http://r.npm.sankuai.com/postcss-selector-parser/download/postcss-selector-parser-6.1.2.tgz#27ecb41fb0e3b6ba7a1ec84fff347f734c7929de"
  integrity sha1-J+y0H7Djtrp6HshP/zR/c0x5Kd4=
  dependencies:
    cssesc "^3.0.0"
    util-deprecate "^1.0.2"

postcss@^8.5.3:
  version "8.5.3"
  resolved "http://r.npm.sankuai.com/postcss/download/postcss-8.5.3.tgz#1463b6f1c7fb16fe258736cba29a2de35237eafb"
  integrity sha1-FGO28cf7Fv4lhzbLopot41I36vs=
  dependencies:
    nanoid "^3.3.8"
    picocolors "^1.1.1"
    source-map-js "^1.2.1"

prelude-ls@^1.2.1:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/prelude-ls/download/prelude-ls-1.2.1.tgz#debc6489d7a6e6b0e7611888cec880337d316396"
  integrity sha1-3rxkidem5rDnYRiIzsiAM30xY5Y=

prettier-linter-helpers@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/prettier-linter-helpers/download/prettier-linter-helpers-1.0.0.tgz#d23d41fe1375646de2d0104d3454a3008802cf7b"
  integrity sha1-0j1B/hN1ZG3i0BBNNFSjAIgCz3s=
  dependencies:
    fast-diff "^1.1.2"

prettier@3.5.3:
  version "3.5.3"
  resolved "http://r.npm.sankuai.com/prettier/download/prettier-3.5.3.tgz#4fc2ce0d657e7a02e602549f053b239cb7dfe1b5"
  integrity sha1-T8LODWV+egLmAlSfBTsjnLff4bU=

pretty-ms@^9.2.0:
  version "9.2.0"
  resolved "http://r.npm.sankuai.com/pretty-ms/download/pretty-ms-9.2.0.tgz#e14c0aad6493b69ed63114442a84133d7e560ef0"
  integrity sha1-4UwKrWSTtp7WMRREKoQTPX5WDvA=
  dependencies:
    parse-ms "^4.0.0"

proto-list@~1.2.1:
  version "1.2.4"
  resolved "http://r.npm.sankuai.com/proto-list/download/proto-list-1.2.4.tgz#212d5bfe1318306a420f6402b8e26ff39647a849"
  integrity sha1-IS1b/hMYMGpCD2QCuOJv85ZHqEk=

punycode@^2.1.0, punycode@^2.3.1:
  version "2.3.1"
  resolved "http://r.npm.sankuai.com/punycode/download/punycode-2.3.1.tgz#027422e2faec0b25e1549c3e1bd8309b9133b6e5"
  integrity sha1-AnQi4vrsCyXhVJw+G9gwm5EztuU=

queue-microtask@^1.2.2:
  version "1.2.3"
  resolved "http://r.npm.sankuai.com/queue-microtask/download/queue-microtask-1.2.3.tgz#4929228bbc724dfac43e0efb058caf7b6cfb6243"
  integrity sha1-SSkii7xyTfrEPg77BYyve2z7YkM=

read-package-json-fast@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/read-package-json-fast/download/read-package-json-fast-4.0.0.tgz#8ccbc05740bb9f58264f400acc0b4b4eee8d1b39"
  integrity sha1-jMvAV0C7n1gmT0AKzAtLTu6NGzk=
  dependencies:
    json-parse-even-better-errors "^4.0.0"
    npm-normalize-package-bin "^4.0.0"

resolve-from@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/resolve-from/download/resolve-from-4.0.0.tgz#4abcd852ad32dd7baabfe9b40e00a36db5f392e6"
  integrity sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=

reusify@^1.0.4:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/reusify/download/reusify-1.1.0.tgz#0fe13b9522e1473f51b558ee796e08f11f9b489f"
  integrity sha1-D+E7lSLhRz9RtVjueW4I8R+bSJ8=

rfdc@^1.4.1:
  version "1.4.1"
  resolved "http://r.npm.sankuai.com/rfdc/download/rfdc-1.4.1.tgz#778f76c4fb731d93414e8f925fbecf64cce7f6ca"
  integrity sha1-d492xPtzHZNBTo+SX77PZMzn9so=

rollup@^4.34.9:
  version "4.41.1"
  resolved "http://r.npm.sankuai.com/rollup/download/rollup-4.41.1.tgz#46ddc1b33cf1b0baa99320d3b0b4973dc2253b6a"
  integrity sha1-Rt3BszzxsLqpkyDTsLSXPcIlO2o=
  dependencies:
    "@types/estree" "1.0.7"
  optionalDependencies:
    "@rollup/rollup-android-arm-eabi" "4.41.1"
    "@rollup/rollup-android-arm64" "4.41.1"
    "@rollup/rollup-darwin-arm64" "4.41.1"
    "@rollup/rollup-darwin-x64" "4.41.1"
    "@rollup/rollup-freebsd-arm64" "4.41.1"
    "@rollup/rollup-freebsd-x64" "4.41.1"
    "@rollup/rollup-linux-arm-gnueabihf" "4.41.1"
    "@rollup/rollup-linux-arm-musleabihf" "4.41.1"
    "@rollup/rollup-linux-arm64-gnu" "4.41.1"
    "@rollup/rollup-linux-arm64-musl" "4.41.1"
    "@rollup/rollup-linux-loongarch64-gnu" "4.41.1"
    "@rollup/rollup-linux-powerpc64le-gnu" "4.41.1"
    "@rollup/rollup-linux-riscv64-gnu" "4.41.1"
    "@rollup/rollup-linux-riscv64-musl" "4.41.1"
    "@rollup/rollup-linux-s390x-gnu" "4.41.1"
    "@rollup/rollup-linux-x64-gnu" "4.41.1"
    "@rollup/rollup-linux-x64-musl" "4.41.1"
    "@rollup/rollup-win32-arm64-msvc" "4.41.1"
    "@rollup/rollup-win32-ia32-msvc" "4.41.1"
    "@rollup/rollup-win32-x64-msvc" "4.41.1"
    fsevents "~2.3.2"

rrweb-cssom@^0.8.0:
  version "0.8.0"
  resolved "http://r.npm.sankuai.com/rrweb-cssom/download/rrweb-cssom-0.8.0.tgz#3021d1b4352fbf3b614aaeed0bc0d5739abe0bc2"
  integrity sha1-MCHRtDUvvzthSq7tC8DVc5q+C8I=

run-applescript@^7.0.0:
  version "7.0.0"
  resolved "http://r.npm.sankuai.com/run-applescript/download/run-applescript-7.0.0.tgz#e5a553c2bffd620e169d276c1cd8f1b64778fbeb"
  integrity sha1-5aVTwr/9Yg4WnSdsHNjxtkd4++s=

run-parallel@^1.1.9:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/run-parallel/download/run-parallel-1.2.0.tgz#66d1368da7bdf921eb9d95bd1a9229e7f21a43ee"
  integrity sha1-ZtE2jae9+SHrnZW9GpIp5/IaQ+4=
  dependencies:
    queue-microtask "^1.2.2"

"safer-buffer@>= 2.1.2 < 3.0.0":
  version "2.1.2"
  resolved "http://r.npm.sankuai.com/safer-buffer/download/safer-buffer-2.1.2.tgz#44fa161b0187b9549dd84bb91802f9bd8385cd6a"
  integrity sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=

saxes@^6.0.0:
  version "6.0.0"
  resolved "http://r.npm.sankuai.com/saxes/download/saxes-6.0.0.tgz#fe5b4a4768df4f14a201b1ba6a65c1f3d9988cc5"
  integrity sha1-/ltKR2jfTxSiAbG6amXB89mYjMU=
  dependencies:
    xmlchars "^2.2.0"

semver@^6.3.1:
  version "6.3.1"
  resolved "http://r.npm.sankuai.com/semver/download/semver-6.3.1.tgz#556d2ef8689146e46dcea4bfdd095f3434dffcb4"
  integrity sha1-VW0u+GiRRuRtzqS/3QlfNDTf/LQ=

semver@^7.5.3, semver@^7.6.0, semver@^7.6.3:
  version "7.7.2"
  resolved "http://r.npm.sankuai.com/semver/download/semver-7.7.2.tgz#67d99fdcd35cec21e6f8b87a7fd515a33f982b58"
  integrity sha1-Z9mf3NNc7CHm+Lh6f9UVoz+YK1g=

shebang-command@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/shebang-command/download/shebang-command-2.0.0.tgz#ccd0af4f8835fbdc265b82461aaf0c36663f34ea"
  integrity sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/shebang-regex/download/shebang-regex-3.0.0.tgz#ae16f1644d873ecad843b0307b143362d4c42172"
  integrity sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=

shell-quote@^1.7.3:
  version "1.8.2"
  resolved "http://r.npm.sankuai.com/shell-quote/download/shell-quote-1.8.2.tgz#d2d83e057959d53ec261311e9e9b8f51dcb2934a"
  integrity sha1-0tg+BXlZ1T7CYTEenpuPUdyyk0o=

siginfo@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/siginfo/download/siginfo-2.0.0.tgz#32e76c70b79724e3bb567cb9d543eb858ccfaf30"
  integrity sha1-MudscLeXJOO7Vny51UPrhYzPrzA=

signal-exit@^4.0.1, signal-exit@^4.1.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/signal-exit/download/signal-exit-4.1.0.tgz#952188c1cbd546070e2dd20d0f41c0ae0530cb04"
  integrity sha1-lSGIwcvVRgcOLdIND0HArgUwywQ=

sirv@^3.0.0, sirv@^3.0.1:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/sirv/download/sirv-3.0.1.tgz#32a844794655b727f9e2867b777e0060fbe07bf3"
  integrity sha1-MqhEeUZVtyf54oZ7d34AYPvge/M=
  dependencies:
    "@polka/url" "^1.0.0-next.24"
    mrmime "^2.0.0"
    totalist "^3.0.0"

source-map-js@^1.2.1:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/source-map-js/download/source-map-js-1.2.1.tgz#1ce5650fddd87abc099eda37dcff024c2667ae46"
  integrity sha1-HOVlD93YerwJnto33P8CTCZnrkY=

speakingurl@^14.0.1:
  version "14.0.1"
  resolved "http://r.npm.sankuai.com/speakingurl/download/speakingurl-14.0.1.tgz#f37ec8ddc4ab98e9600c1c9ec324a8c48d772a53"
  integrity sha1-837I3cSrmOlgDByewySoxI13KlM=

stackback@0.0.2:
  version "0.0.2"
  resolved "http://r.npm.sankuai.com/stackback/download/stackback-0.0.2.tgz#1ac8a0d9483848d1695e418b6d031a3c3ce68e3b"
  integrity sha1-Gsig2Ug4SNFpXkGLbQMaPDzmjjs=

std-env@^3.9.0:
  version "3.9.0"
  resolved "http://r.npm.sankuai.com/std-env/download/std-env-3.9.0.tgz#1a6f7243b339dca4c9fd55e1c7504c77ef23e8f1"
  integrity sha1-Gm9yQ7M53KTJ/VXhx1BMd+8j6PE=

"string-width-cjs@npm:string-width@^4.2.0":
  version "4.2.3"
  resolved "http://r.npm.sankuai.com/string-width/download/string-width-4.2.3.tgz#269c7117d27b05ad2e536830a8ec895ef9c6d010"
  integrity sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string-width@^4.1.0:
  version "4.2.3"
  resolved "http://r.npm.sankuai.com/string-width/download/string-width-4.2.3.tgz#269c7117d27b05ad2e536830a8ec895ef9c6d010"
  integrity sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string-width@^5.0.1, string-width@^5.1.2:
  version "5.1.2"
  resolved "http://r.npm.sankuai.com/string-width/download/string-width-5.1.2.tgz#14f8daec6d81e7221d2a357e668cab73bdbca794"
  integrity sha1-FPja7G2B5yIdKjV+Zoyrc728p5Q=
  dependencies:
    eastasianwidth "^0.2.0"
    emoji-regex "^9.2.2"
    strip-ansi "^7.0.1"

"strip-ansi-cjs@npm:strip-ansi@^6.0.1":
  version "6.0.1"
  resolved "http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-6.0.1.tgz#9e26c63d30f53443e9489495b2105d37b67a85d9"
  integrity sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=
  dependencies:
    ansi-regex "^5.0.1"

strip-ansi@^6.0.0, strip-ansi@^6.0.1:
  version "6.0.1"
  resolved "http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-6.0.1.tgz#9e26c63d30f53443e9489495b2105d37b67a85d9"
  integrity sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=
  dependencies:
    ansi-regex "^5.0.1"

strip-ansi@^7.0.1:
  version "7.1.0"
  resolved "http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-7.1.0.tgz#d5b6568ca689d8561370b0707685d22434faff45"
  integrity sha1-1bZWjKaJ2FYTcLBwdoXSJDT6/0U=
  dependencies:
    ansi-regex "^6.0.1"

strip-final-newline@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/strip-final-newline/download/strip-final-newline-4.0.0.tgz#35a369ec2ac43df356e3edd5dcebb6429aa1fa5c"
  integrity sha1-NaNp7CrEPfNW4+3V3Ou2Qpqh+lw=

strip-json-comments@^3.1.1:
  version "3.1.1"
  resolved "http://r.npm.sankuai.com/strip-json-comments/download/strip-json-comments-3.1.1.tgz#31f1281b3832630434831c310c01cccda8cbe006"
  integrity sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY=

superjson@^2.2.2:
  version "2.2.2"
  resolved "http://r.npm.sankuai.com/superjson/download/superjson-2.2.2.tgz#9d52bf0bf6b5751a3c3472f1292e714782ba3173"
  integrity sha1-nVK/C/a1dRo8NHLxKS5xR4K6MXM=
  dependencies:
    copy-anything "^3.0.2"

supports-color@^7.1.0:
  version "7.2.0"
  resolved "http://r.npm.sankuai.com/supports-color/download/supports-color-7.2.0.tgz#1b7dcdcb32b8138801b3e478ba6a51caa89648da"
  integrity sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=
  dependencies:
    has-flag "^4.0.0"

symbol-tree@^3.2.4:
  version "3.2.4"
  resolved "http://r.npm.sankuai.com/symbol-tree/download/symbol-tree-3.2.4.tgz#430637d248ba77e078883951fb9aa0eed7c63fa2"
  integrity sha1-QwY30ki6d+B4iDlR+5qg7tfGP6I=

synckit@^0.11.0:
  version "0.11.6"
  resolved "http://r.npm.sankuai.com/synckit/download/synckit-0.11.6.tgz#e742a0c27bbc1fbc96f2010770521015cca7ed5c"
  integrity sha1-50Kgwnu8H7yW8gEHcFIQFcyn7Vw=
  dependencies:
    "@pkgr/core" "^0.2.4"

tinybench@^2.9.0:
  version "2.9.0"
  resolved "http://r.npm.sankuai.com/tinybench/download/tinybench-2.9.0.tgz#103c9f8ba6d7237a47ab6dd1dcff77251863426b"
  integrity sha1-EDyfi6bXI3pHq23R3P93JRhjQms=

tinyexec@^0.3.2:
  version "0.3.2"
  resolved "http://r.npm.sankuai.com/tinyexec/download/tinyexec-0.3.2.tgz#941794e657a85e496577995c6eef66f53f42b3d2"
  integrity sha1-lBeU5leoXklld5lcbu9m9T9Cs9I=

tinyglobby@^0.2.13:
  version "0.2.14"
  resolved "http://r.npm.sankuai.com/tinyglobby/download/tinyglobby-0.2.14.tgz#5280b0cf3f972b050e74ae88406c0a6a58f4079d"
  integrity sha1-UoCwzz+XKwUOdK6IQGwKalj0B50=
  dependencies:
    fdir "^6.4.4"
    picomatch "^4.0.2"

tinypool@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/tinypool/download/tinypool-1.0.2.tgz#706193cc532f4c100f66aa00b01c42173d9051b2"
  integrity sha1-cGGTzFMvTBAPZqoAsBxCFz2QUbI=

tinyrainbow@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/tinyrainbow/download/tinyrainbow-2.0.0.tgz#9509b2162436315e80e3eee0fcce4474d2444294"
  integrity sha1-lQmyFiQ2MV6A4+7g/M5EdNJEQpQ=

tinyspy@^3.0.2:
  version "3.0.2"
  resolved "http://r.npm.sankuai.com/tinyspy/download/tinyspy-3.0.2.tgz#86dd3cf3d737b15adcf17d7887c84a75201df20a"
  integrity sha1-ht0889c3sVrc8X14h8hKdSAd8go=

tldts-core@^6.1.86:
  version "6.1.86"
  resolved "http://r.npm.sankuai.com/tldts-core/download/tldts-core-6.1.86.tgz#a93e6ed9d505cb54c542ce43feb14c73913265d8"
  integrity sha1-qT5u2dUFy1TFQs5D/rFMc5EyZdg=

tldts@^6.1.32:
  version "6.1.86"
  resolved "http://r.npm.sankuai.com/tldts/download/tldts-6.1.86.tgz#087e0555b31b9725ee48ca7e77edc56115cd82f7"
  integrity sha1-CH4FVbMblyXuSMp+d+3FYRXNgvc=
  dependencies:
    tldts-core "^6.1.86"

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "http://r.npm.sankuai.com/to-regex-range/download/to-regex-range-5.0.1.tgz#1648c44aae7c8d988a326018ed72f5b4dd0392e4"
  integrity sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=
  dependencies:
    is-number "^7.0.0"

totalist@^3.0.0:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/totalist/download/totalist-3.0.1.tgz#ba3a3d600c915b1a97872348f79c127475f6acf8"
  integrity sha1-ujo9YAyRWxqXhyNI95wSdHX2rPg=

tough-cookie@^5.1.1:
  version "5.1.2"
  resolved "http://r.npm.sankuai.com/tough-cookie/download/tough-cookie-5.1.2.tgz#66d774b4a1d9e12dc75089725af3ac75ec31bed7"
  integrity sha1-Ztd0tKHZ4S3HUIlyWvOsdewxvtc=
  dependencies:
    tldts "^6.1.32"

tr46@^5.1.0:
  version "5.1.1"
  resolved "http://r.npm.sankuai.com/tr46/download/tr46-5.1.1.tgz#96ae867cddb8fdb64a49cc3059a8d428bcf238ca"
  integrity sha1-lq6GfN24/bZKScwwWajUKLzyOMo=
  dependencies:
    punycode "^2.3.1"

ts-api-utils@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/ts-api-utils/download/ts-api-utils-2.1.0.tgz#595f7094e46eed364c13fd23e75f9513d29baf91"
  integrity sha1-WV9wlORu7TZME/0j51+VE9Kbr5E=

type-check@^0.4.0, type-check@~0.4.0:
  version "0.4.0"
  resolved "http://r.npm.sankuai.com/type-check/download/type-check-0.4.0.tgz#07b8203bfa7056c0657050e3ccd2c37730bab8f1"
  integrity sha1-B7ggO/pwVsBlcFDjzNLDdzC6uPE=
  dependencies:
    prelude-ls "^1.2.1"

type-fest@^0.20.2:
  version "0.20.2"
  resolved "http://r.npm.sankuai.com/type-fest/download/type-fest-0.20.2.tgz#1bf207f4b28f91583666cb5fbd327887301cd5f4"
  integrity sha1-G/IH9LKPkVg2ZstfvTJ4hzAc1fQ=

typescript-eslint@^8.26.0:
  version "8.33.0"
  resolved "http://r.npm.sankuai.com/typescript-eslint/download/typescript-eslint-8.33.0.tgz#89f733a90edc6abe0994b6130b964e781a1ba82f"
  integrity sha1-ifczqQ7car4JlLYTC5ZOeBobqC8=
  dependencies:
    "@typescript-eslint/eslint-plugin" "8.33.0"
    "@typescript-eslint/parser" "8.33.0"
    "@typescript-eslint/utils" "8.33.0"

typescript@~5.8.0:
  version "5.8.3"
  resolved "http://r.npm.sankuai.com/typescript/download/typescript-5.8.3.tgz#92f8a3e5e3cf497356f4178c34cd65a7f5e8440e"
  integrity sha1-kvij5ePPSXNW9BeMNM1lp/XoRA4=

undici-types@~6.21.0:
  version "6.21.0"
  resolved "http://r.npm.sankuai.com/undici-types/download/undici-types-6.21.0.tgz#691d00af3909be93a7faa13be61b3a5b50ef12cb"
  integrity sha1-aR0ArzkJvpOn+qE75hs6W1DvEss=

unicorn-magic@^0.3.0:
  version "0.3.0"
  resolved "http://r.npm.sankuai.com/unicorn-magic/download/unicorn-magic-0.3.0.tgz#4efd45c85a69e0dd576d25532fbfa22aa5c8a104"
  integrity sha1-Tv1FyFpp4N1XbSVTL7+iKqXIoQQ=

universalify@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/universalify/download/universalify-2.0.1.tgz#168efc2180964e6386d061e094df61afe239b18d"
  integrity sha1-Fo78IYCWTmOG0GHglN9hr+I5sY0=

update-browserslist-db@^1.1.3:
  version "1.1.3"
  resolved "http://r.npm.sankuai.com/update-browserslist-db/download/update-browserslist-db-1.1.3.tgz#348377dd245216f9e7060ff50b15a1b740b75420"
  integrity sha1-NIN33SRSFvnnBg/1CxWht0C3VCA=
  dependencies:
    escalade "^3.2.0"
    picocolors "^1.1.1"

uri-js@^4.2.2:
  version "4.4.1"
  resolved "http://r.npm.sankuai.com/uri-js/download/uri-js-4.4.1.tgz#9b1a52595225859e55f669d928f88c6c57f2a77e"
  integrity sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=
  dependencies:
    punycode "^2.1.0"

util-deprecate@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/util-deprecate/download/util-deprecate-1.0.2.tgz#450d4dc9fa70de732762fbd2d4a28981419a0ccf"
  integrity sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=

vite-hot-client@^2.0.4:
  version "2.0.4"
  resolved "http://r.npm.sankuai.com/vite-hot-client/download/vite-hot-client-2.0.4.tgz#db383e0337c758fbabf14dad26f9a1bcb9e9e175"
  integrity sha1-2zg+AzfHWPur8U2tJvmhvLnp4XU=

vite-node@3.1.4:
  version "3.1.4"
  resolved "http://r.npm.sankuai.com/vite-node/download/vite-node-3.1.4.tgz#13f10b2cb155197a971cb2761664ec952c6cae18"
  integrity sha1-E/ELLLFVGXqXHLJ2FmTslSxsrhg=
  dependencies:
    cac "^6.7.14"
    debug "^4.4.0"
    es-module-lexer "^1.7.0"
    pathe "^2.0.3"
    vite "^5.0.0 || ^6.0.0"

vite-plugin-inspect@0.8.9:
  version "0.8.9"
  resolved "http://r.npm.sankuai.com/vite-plugin-inspect/download/vite-plugin-inspect-0.8.9.tgz#01a7e484ccbc12a8c86ee8bc90efe13aeb0fed1b"
  integrity sha1-AafkhMy8EqjIbui8kO/hOusP7Rs=
  dependencies:
    "@antfu/utils" "^0.7.10"
    "@rollup/pluginutils" "^5.1.3"
    debug "^4.3.7"
    error-stack-parser-es "^0.1.5"
    fs-extra "^11.2.0"
    open "^10.1.0"
    perfect-debounce "^1.0.0"
    picocolors "^1.1.1"
    sirv "^3.0.0"

vite-plugin-vue-devtools@^7.7.2:
  version "7.7.6"
  resolved "http://r.npm.sankuai.com/vite-plugin-vue-devtools/download/vite-plugin-vue-devtools-7.7.6.tgz#d76e61a2d53e88e72aada9d3f89529b3acfdb1ae"
  integrity sha1-125hotU+iOcqranT+JUps6z9sa4=
  dependencies:
    "@vue/devtools-core" "^7.7.6"
    "@vue/devtools-kit" "^7.7.6"
    "@vue/devtools-shared" "^7.7.6"
    execa "^9.5.2"
    sirv "^3.0.1"
    vite-plugin-inspect "0.8.9"
    vite-plugin-vue-inspector "^5.3.1"

vite-plugin-vue-inspector@^5.3.1:
  version "5.3.1"
  resolved "http://r.npm.sankuai.com/vite-plugin-vue-inspector/download/vite-plugin-vue-inspector-5.3.1.tgz#e57abdb11b15dea0f5db0b0af2e2d0b236c18717"
  integrity sha1-5Xq9sRsV3qD12wsK8uLQsjbBhxc=
  dependencies:
    "@babel/core" "^7.23.0"
    "@babel/plugin-proposal-decorators" "^7.23.0"
    "@babel/plugin-syntax-import-attributes" "^7.22.5"
    "@babel/plugin-syntax-import-meta" "^7.10.4"
    "@babel/plugin-transform-typescript" "^7.22.15"
    "@vue/babel-plugin-jsx" "^1.1.5"
    "@vue/compiler-dom" "^3.3.4"
    kolorist "^1.8.0"
    magic-string "^0.30.4"

"vite@^5.0.0 || ^6.0.0", vite@^6.2.4:
  version "6.3.5"
  resolved "http://r.npm.sankuai.com/vite/download/vite-6.3.5.tgz#fec73879013c9c0128c8d284504c6d19410d12a3"
  integrity sha1-/sc4eQE8nAEoyNKEUExtGUENEqM=
  dependencies:
    esbuild "^0.25.0"
    fdir "^6.4.4"
    picomatch "^4.0.2"
    postcss "^8.5.3"
    rollup "^4.34.9"
    tinyglobby "^0.2.13"
  optionalDependencies:
    fsevents "~2.3.3"

vitest@^3.1.1:
  version "3.1.4"
  resolved "http://r.npm.sankuai.com/vitest/download/vitest-3.1.4.tgz#5f495b7dbb1d4d208b88508cd4dfceb006f8b7e6"
  integrity sha1-X0lbfbsdTSCLiFCM1N/OsAb4t+Y=
  dependencies:
    "@vitest/expect" "3.1.4"
    "@vitest/mocker" "3.1.4"
    "@vitest/pretty-format" "^3.1.4"
    "@vitest/runner" "3.1.4"
    "@vitest/snapshot" "3.1.4"
    "@vitest/spy" "3.1.4"
    "@vitest/utils" "3.1.4"
    chai "^5.2.0"
    debug "^4.4.0"
    expect-type "^1.2.1"
    magic-string "^0.30.17"
    pathe "^2.0.3"
    std-env "^3.9.0"
    tinybench "^2.9.0"
    tinyexec "^0.3.2"
    tinyglobby "^0.2.13"
    tinypool "^1.0.2"
    tinyrainbow "^2.0.0"
    vite "^5.0.0 || ^6.0.0"
    vite-node "3.1.4"
    why-is-node-running "^2.3.0"

vscode-uri@^3.0.8:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/vscode-uri/download/vscode-uri-3.1.0.tgz#dd09ec5a66a38b5c3fffc774015713496d14e09c"
  integrity sha1-3QnsWmaji1w//8d0AVcTSW0U4Jw=

vue-component-type-helpers@^2.0.0:
  version "2.2.10"
  resolved "http://r.npm.sankuai.com/vue-component-type-helpers/download/vue-component-type-helpers-2.2.10.tgz#126a7d7258f7458c66dfe234a612500c90c4838c"
  integrity sha1-Emp9clj3RYxm3+I0phJQDJDEg4w=

vue-eslint-parser@^10.1.1:
  version "10.1.3"
  resolved "http://r.npm.sankuai.com/vue-eslint-parser/download/vue-eslint-parser-10.1.3.tgz#96457823a5915a62001798cfd9cc15a89067bf81"
  integrity sha1-lkV4I6WRWmIAF5jP2cwVqJBnv4E=
  dependencies:
    debug "^4.4.0"
    eslint-scope "^8.2.0"
    eslint-visitor-keys "^4.2.0"
    espree "^10.3.0"
    esquery "^1.6.0"
    lodash "^4.17.21"
    semver "^7.6.3"

vue-router@^4.5.0:
  version "4.5.1"
  resolved "http://r.npm.sankuai.com/vue-router/download/vue-router-4.5.1.tgz#47bffe2d3a5479d2886a9a244547a853aa0abf69"
  integrity sha1-R7/+LTpUedKIapokRUeoU6oKv2k=
  dependencies:
    "@vue/devtools-api" "^6.6.4"

vue-tsc@^2.2.8:
  version "2.2.10"
  resolved "http://r.npm.sankuai.com/vue-tsc/download/vue-tsc-2.2.10.tgz#7b51a666cb90788884efd0caedc69fc1fc9c5b78"
  integrity sha1-e1GmZsuQeIiE79DK7cafwfycW3g=
  dependencies:
    "@volar/typescript" "~2.4.11"
    "@vue/language-core" "2.2.10"

vue@^3.5.13:
  version "3.5.15"
  resolved "http://r.npm.sankuai.com/vue/download/vue-3.5.15.tgz#5896569a33a1bcafd764c6b27e4e6f8cb55f4bee"
  integrity sha1-WJZWmjOhvK/XZMayfk5vjLVfS+4=
  dependencies:
    "@vue/compiler-dom" "3.5.15"
    "@vue/compiler-sfc" "3.5.15"
    "@vue/runtime-dom" "3.5.15"
    "@vue/server-renderer" "3.5.15"
    "@vue/shared" "3.5.15"

w3c-xmlserializer@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/w3c-xmlserializer/download/w3c-xmlserializer-5.0.0.tgz#f925ba26855158594d907313cedd1476c5967f6c"
  integrity sha1-+SW6JoVRWFlNkHMTzt0UdsWWf2w=
  dependencies:
    xml-name-validator "^5.0.0"

webidl-conversions@^7.0.0:
  version "7.0.0"
  resolved "http://r.npm.sankuai.com/webidl-conversions/download/webidl-conversions-7.0.0.tgz#256b4e1882be7debbf01d05f0aa2039778ea080a"
  integrity sha1-JWtOGIK+feu/AdBfCqIDl3jqCAo=

whatwg-encoding@^3.1.1:
  version "3.1.1"
  resolved "http://r.npm.sankuai.com/whatwg-encoding/download/whatwg-encoding-3.1.1.tgz#d0f4ef769905d426e1688f3e34381a99b60b76e5"
  integrity sha1-0PTvdpkF1CbhaI8+NDgambYLduU=
  dependencies:
    iconv-lite "0.6.3"

whatwg-mimetype@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/whatwg-mimetype/download/whatwg-mimetype-4.0.0.tgz#bc1bf94a985dc50388d54a9258ac405c3ca2fc0a"
  integrity sha1-vBv5SphdxQOI1UqSWKxAXDyi/Ao=

whatwg-url@^14.0.0, whatwg-url@^14.1.1:
  version "14.2.0"
  resolved "http://r.npm.sankuai.com/whatwg-url/download/whatwg-url-14.2.0.tgz#4ee02d5d725155dae004f6ae95c73e7ef5d95663"
  integrity sha1-TuAtXXJRVdrgBPaulcc+fvXZVmM=
  dependencies:
    tr46 "^5.1.0"
    webidl-conversions "^7.0.0"

which@^2.0.1:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/which/download/which-2.0.2.tgz#7c6a8dd0a636a0327e10b59c9286eee93f3f51b1"
  integrity sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=
  dependencies:
    isexe "^2.0.0"

which@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/which/download/which-5.0.0.tgz#d93f2d93f79834d4363c7d0c23e00d07c466c8d6"
  integrity sha1-2T8tk/eYNNQ2PH0MI+ANB8RmyNY=
  dependencies:
    isexe "^3.1.1"

why-is-node-running@^2.3.0:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/why-is-node-running/download/why-is-node-running-2.3.0.tgz#a3f69a97107f494b3cdc3bdddd883a7d65cebf04"
  integrity sha1-o/aalxB/SUs83Dvd3Yg6fWXOvwQ=
  dependencies:
    siginfo "^2.0.0"
    stackback "0.0.2"

word-wrap@^1.2.5:
  version "1.2.5"
  resolved "http://r.npm.sankuai.com/word-wrap/download/word-wrap-1.2.5.tgz#d2c45c6dd4fbce621a66f136cbe328afd0410b34"
  integrity sha1-0sRcbdT7zmIaZvE2y+Mor9BBCzQ=

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0":
  version "7.0.0"
  resolved "http://r.npm.sankuai.com/wrap-ansi/download/wrap-ansi-7.0.0.tgz#67e145cff510a6a6984bdf1152911d69d2eb9e43"
  integrity sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^8.1.0:
  version "8.1.0"
  resolved "http://r.npm.sankuai.com/wrap-ansi/download/wrap-ansi-8.1.0.tgz#56dc22368ee570face1b49819975d9b9a5ead214"
  integrity sha1-VtwiNo7lcPrOG0mBmXXZuaXq0hQ=
  dependencies:
    ansi-styles "^6.1.0"
    string-width "^5.0.1"
    strip-ansi "^7.0.1"

ws@^8.18.0:
  version "8.18.2"
  resolved "http://r.npm.sankuai.com/ws/download/ws-8.18.2.tgz#42738b2be57ced85f46154320aabb51ab003705a"
  integrity sha1-QnOLK+V87YX0YVQyCqu1GrADcFo=

xml-name-validator@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/xml-name-validator/download/xml-name-validator-4.0.0.tgz#79a006e2e63149a8600f15430f0a4725d1524835"
  integrity sha1-eaAG4uYxSahgDxVDDwpHJdFSSDU=

xml-name-validator@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/xml-name-validator/download/xml-name-validator-5.0.0.tgz#82be9b957f7afdacf961e5980f1bf227c0bf7673"
  integrity sha1-gr6blX96/az5YeWYDxvyJ8C/dnM=

xmlchars@^2.2.0:
  version "2.2.0"
  resolved "http://r.npm.sankuai.com/xmlchars/download/xmlchars-2.2.0.tgz#060fe1bcb7f9c76fe2a17db86a9bc3ab894210cb"
  integrity sha1-Bg/hvLf5x2/ioX24apvDq4lCEMs=

yallist@^3.0.2:
  version "3.1.1"
  resolved "http://r.npm.sankuai.com/yallist/download/yallist-3.1.1.tgz#dbb7daf9bfd8bac9ab45ebf602b8cbad0d5d08fd"
  integrity sha1-27fa+b/YusmrRev2ArjLrQ1dCP0=

yocto-queue@^0.1.0:
  version "0.1.0"
  resolved "http://r.npm.sankuai.com/yocto-queue/download/yocto-queue-0.1.0.tgz#0294eb3dee05028d31ee1a5fa2c556a6aaf10a1b"
  integrity sha1-ApTrPe4FAo0x7hpfosVWpqrxChs=

yoctocolors@^2.1.1:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/yoctocolors/download/yoctocolors-2.1.1.tgz#e0167474e9fbb9e8b3ecca738deaa61dd12e56fc"
  integrity sha1-4BZ0dOn7ueiz7MpzjeqmHdEuVvw=
